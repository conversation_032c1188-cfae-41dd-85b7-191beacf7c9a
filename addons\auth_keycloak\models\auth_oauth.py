# Copyright 2018 Camptocamp SA
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html)
from odoo import fields, models, api

class OAuthProvider(models.Model):
    _inherit = 'auth.oauth.provider'

    client_secret = fields.Char()
    users_endpoint = fields.Char(
        help='Keycloak user endpoint, e.g. http://keycloak.local/auth/admin/realms/{realm}/users',
    )
    superuser = fields.Char(
        help='Keycloak admin username with user management rights.',
    )
    superuser_pwd = fields.Char(
        help='Password of the Keycloak admin user.',
        password=True,
    )
    users_management_enabled = fields.Boolean(
        compute='_compute_users_management_enabled'
    )

    @api.depends(
        'enabled',
        'users_endpoint',
        'superuser',
        'superuser_pwd',
    )
    def _compute_users_management_enabled(self):
        for item in self:
            item.users_management_enabled = all([
                item.enabled,
                item.users_endpoint,
                item.superuser,
                item.superuser_pwd,
            ])
