# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_oidc
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-10-15 19:36+0000\n"
"Last-Translator: Ivorra78 <<EMAIL>>\n"
"Language-Team: none\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__flow
msgid "Auth Flow"
msgstr "Flujo de autenticación"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__client_secret
msgid "Client Secret"
msgstr "Secreto del cliente"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__code_verifier
msgid "Code Verifier"
msgstr "Verificador del código"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__jwks_uri
msgid "JWKS URL"
msgstr "URL JWKS"

#. module: auth_oidc
#: model:auth.oauth.provider,body:auth_oidc.provider_azuread_multi
#: model:auth.oauth.provider,body:auth_oidc.provider_azuread_single
msgid "Log in with Microsoft"
msgstr "Iniciar sesión con Microsoft"

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__access_token
msgid "OAuth2"
msgstr "OAuth2"

#. module: auth_oidc
#: model:ir.model,name:auth_oidc.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr "Proveedor OAuth2"

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__id_token_code
msgid "OpenID Connect (authorization code flow)"
msgstr "OpenID Connect (flujo de código de autorización)"

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__id_token
msgid "OpenID Connect (implicit flow, not recommended)"
msgstr "OpenID Connect (flujo implícito, no recomendado)"

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__token_endpoint
msgid "Required for OpenID Connect authorization code flow."
msgstr "Necesario para el flujo de código de autorización de OpenID Connect."

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__jwks_uri
msgid "Required for OpenID Connect."
msgstr "Requerido para OpenID Connect."

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__token_map
msgid ""
"Some Oauth providers don't map keys in their responses exactly as required.  "
"It is important to ensure user_id and email at least are mapped. For OpenID "
"Connect user_id is the sub key in the standard."
msgstr ""
"Algunos proveedores de Oauth no mapean las claves en sus respuestas "
"exactamente como se requiere.  Es importante asegurarse de que al menos "
"user_id y email están mapeados. Para OpenID Connect user_id es la subclave "
"en el estándar."

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__token_map
msgid "Token Map"
msgstr "Mapa de Símbolos"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__token_endpoint
msgid "Token URL"
msgstr "URL de la ficha"

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__code_verifier
msgid "Used for PKCE."
msgstr "Utilizado para PKCE."

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__client_secret
msgid ""
"Used in OpenID Connect authorization code flow for confidential clients."
msgstr ""
"Se utiliza en el flujo de código de autorización de OpenID Connect para "
"clientes confidenciales."

#. module: auth_oidc
#: model:ir.model,name:auth_oidc.model_res_users
msgid "User"
msgstr "Usuario"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__validation_endpoint
msgid "UserInfo URL"
msgstr "URL de información del usuario"

#. module: auth_oidc
#: model_terms:ir.ui.view,arch_db:auth_oidc.view_oidc_provider_form
msgid "e.g from:to upn:email sub:user_id"
msgstr "p.ej. from:to upn:email sub:user_id"

#. module: auth_oidc
#: model:auth.oauth.provider,body:auth_oidc.local_keycloak
msgid "keycloak:8080 on localhost"
msgstr "keycloak:8080 en el servidor local"
