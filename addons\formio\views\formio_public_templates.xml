<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <template id="formio_form_public_embed" name="formio.form - embed">
        &lt;!DOCTYPE html&gt;
        <html>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>

                <script type="text/javascript" src="/formio/static/lib/iframe-resizer/iframeResizer.contentWindow.min.js"></script>
                <script type="text/javascript" src="/web/static/lib/jquery/jquery.js"></script>
                <script type="text/javascript" src="/web/static/lib/owl/owl.js"></script>

                <script type="module" t-attf-src="/formio/static/src/js/form/public_app.js?{{ uuid }}"/>

                <t t-foreach="formio_css_assets" t-as="css">
                    <link rel="stylesheet" type="text/css" t-att-href="css.url"/>
                </t>
                <t t-foreach="formio_js_assets" t-as="js">
                    <script type="text/javascript" t-att-src="js.url"></script>
                </t>
                <link rel="stylesheet" t-attf-href="/formio/static/src/css/formio_form_embed.css?{{ uuid }}"/>
            </head>
            <body data-iframe-height="1">
                <h3 t-if="form.show_title" class="formio_form_title"><span name="title" t-out="form.title"/></h3>
                <div t-if="form.show_id or form.show_uuid or form.show_state or form.show_user_metadata" class="formio_form_dock float-right">
                    <ul>
                        <li t-if="form.assigned_partner_id and form.show_user_metadata" class="assigned">
                            <span>Assigned to:</span> <span t-out="form.assigned_partner_name"/>
                        </li>
                        <li t-if="form.submission_data and form.show_user_metadata" class="submission">
                            <span>Submission by:</span> <span t-out="form.submission_partner_name"/> (<span t-field="form.submission_date"/>)
                        </li>
                        <li t-if="form.show_id" class="form_id">
                            <span>ID:</span> <span t-out="form.id"/>
                        </li>
                        <li t-if="form.show_state" class="form_state">
                            <t t-if="form.state == 'PENDING'">
                                <span t-attf-class="badge mb-1 badge-pill badge-info">State: <span><t t-out="form.display_state"/></span></span>
                            </t>
                            <t t-elif="form.state == 'DRAFT'">
                                <span t-attf-class="badge mb-1 badge-pill badge-warning">State: <span t-out="form.display_state"/></span>
                            </t>
                            <t t-elif="form.state == 'COMPLETE'">
                                <span t-attf-class="badge mb-1 badge-pill badge-success">State: <span><t t-out="form.display_state"/></span></span>
                            </t>
                            <t t-elif="form.state == 'CANCEL'">
                                <span t-attf-class="badge mb-1 badge-pill badge-dark">State: <span><t t-out="form.display_state"/></span></span>
                            </t>
                            <t t-else="">
                                <span t-attf-class="badge mb-1 badge-pill badge-light">State: <span><t t-out="form.display_state"/></span></span>
                            </t>
                        </li>
                    </ul>
                </div>
                <t t-if="len(form_languages) > 1">
                    <div class="formio_languages">
                        <t t-foreach="form_languages" t-as="lang">
                            <button class="btn btn-sm btn-default" t-att-lang="lang.formio_ietf_code" t-attf-onclick="setLanguage('{{ lang.formio_ietf_code }}', this)">
                                <span t-out="lang.name"/>
                            </button>
                        </t>
                    </div>
                </t>

                <div id="formio_form_server_error" class="alert alert-danger d-none">
                    <span id="formio_form_server_error_message"/>
                    <pre id="formio_form_server_error_traceback" class="d-none mt-4"/>
                </div>

                <!-- form shall be mounted here -->
                <div id="formio_form_app">
                    <div id="formio_form_loading_overlay"></div>
                </div>

                <div t-if="form.show_uuid" class="float-right">
                    <small class="text-muted">UUID: <span t-out="form.uuid"/></small>
                </div>
                <input type="hidden" id="formio_form_uuid" name="uuid" t-att-value="form.uuid"/>
            </body>
        </html>
    </template>

    <template id="formio_form_public_new_embed" name="formio.form - public new - embed">
        &lt;!DOCTYPE html&gt;
        <html>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>

                <script type="text/javascript" src="/formio/static/lib/iframe-resizer/iframeResizer.contentWindow.min.js"></script>
                <script type="text/javascript" src="/web/static/lib/jquery/jquery.js"></script>
                <script type="text/javascript" src="/web/static/lib/owl/owl.js"></script>

                <script type="module" t-attf-src="/formio/static/src/js/form/public_new_app.js?{{ uuid }}"/>

                <t t-foreach="formio_css_assets" t-as="css">
                    <link rel="stylesheet" type="text/css" t-att-href="css.url"/>
                </t>
                <t t-foreach="formio_js_assets" t-as="js">
                    <script type="text/javascript" t-att-src="js.url"></script>
                </t>
                <link rel="stylesheet" t-attf-href="/formio/static/src/css/formio_form_embed.css?{{ uuid }}"/>
            </head>
            <body data-iframe-height="1">
                <t t-if="builder">
                    <h3 t-if="builder.show_form_title" class="formio_form_title"><span name="title" t-out="builder.title"/></h3>
                </t>

                <t t-if="len(form_languages) > 1">
                    <div class="formio_languages">
                        <t t-foreach="form_languages" t-as="lang">
                            <button class="btn btn-sm btn-default" t-att-lang="lang.formio_ietf_code" t-attf-onclick="setLanguage('{{ lang.formio_ietf_code }}', this)">
                                <span t-out="lang.name"/>
                            </button>
                        </t>
                    </div>
                </t>

                <div id="formio_form_server_error" class="alert alert-danger d-none">
                    <span id="formio_form_server_error_message"/>
                    <pre id="formio_form_server_error_traceback" class="d-none mt-4"/>
                </div>

                <!-- form shall be mounted here -->
                <div id="formio_form_app">
                    <div id="formio_form_loading_overlay"></div>
                </div>

                <t t-if="builder.uuid">
                    <input type="hidden" id="formio_builder_uuid" name="formio_builder_uuid" t-att-value="builder.uuid"/>
                </t>
            </body>
        </html>
    </template>
</odoo>
