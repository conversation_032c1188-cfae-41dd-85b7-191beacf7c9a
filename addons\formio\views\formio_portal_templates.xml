<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (https://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <template id="portal_layout" name="Portal layout: formio menu entry" inherit_id="portal.portal_breadcrumbs" priority="40">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <li t-if="page_name == 'formio' or formio" t-attf-class="breadcrumb-item #{'active ' if not formio else ''}">
                <a t-if="form" t-attf-href="/my/formio?{{ keep_query() }}">Forms</a>
                <t t-else="">Forms</t>
            </li>
            <li t-if="form" class="breadcrumb-item active">
                <span t-field="form.title"/>
            </li>
        </xpath>
    </template>

    <template id="portal_my_home" name="Forms" customize_show="True" inherit_id="portal.portal_my_home" priority="20">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="before">
            <t t-set="portal_service_category_enable" t-value="True"/>
        </xpath>
        <div id="portal_service_category" position="inside">
            <t t-call="portal.portal_docs_entry">
                <!-- <t t-set="icon" t-value="'/formio/static/src/img/formio.svg'"/> -->
                <t t-set="icon" t-value="'/web/static/img/folder.svg'"/>
                <t t-set="title">Forms</t>
                <t t-set="url" t-value="'/my/formio'"/>
                <t t-set="text">Submit and view your forms</t>
                <t t-set="placeholder_count" t-value="'form_count'"/>
            </t>
        </div>
    </template>

    <template id="portal_my_formio" name="My Forms">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>
            <t t-call="portal.portal_searchbar">
                <t t-set="title">Forms</t>
            </t>
            <t t-if="not forms">
                <div class="alert alert-warning mt8" role="alert">
                    There are no Forms.
                </div>
            </t>
            <t t-if="builders_create_form">
                <div class="o_dropdown_kanban dropdown mt8 mb8">
                    <a class="dropdown-toggle btn btn-primary" role="button" data-bs-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                        Submit a Form
                    </a>
                    <div class="dropdown-menu" role="menu">
                        <t t-foreach="builders_create_form" t-as="builder">
                            <a t-attf-href="/my/formio/form/new/{{ builder.name }}" role="menuitem" class="dropdown-item">
                                <span t-field="builder.title"/>
                            </a>
                        </t>
                    </div>
                </div>
            </t>
            <t t-if="forms" t-call="portal.portal_table">
                <thead>
                    <th>Title</th>
                    <th>Resource</th>
                    <th>Created on</th>
                    <th>Submission date</th>
                    <th>State</th>
                    <th>Actions</th>
                </thead>
                <tbody>
                    <tr t-foreach="forms" t-as="form">
                        <td>
                            <span t-field="form.title"/> <small class="text-muted">#<span t-field="form.id"/></small>
                        </td>
                        <td>
                            <span t-field="form.res_name"/>
                        </td>
                        <td>
                            <span t-field="form.create_date"/>
                        </td>
                        <td>
                            <span t-field="form.submission_date"/>
                        </td>
                        <td>
                            <span t-field="form.state"/>
                        </td>
                        <td class="formio_form_actions">
                            <a t-if="form.state in ['PENDING', 'DRAFT']" role="button" class="btn btn-sm btn-primary"
                               t-attf-href="/my/formio/form/{{ form.uuid }}?{{ keep_query() }}">
                               <i class="fa fa-arrow-circle-right"/> <span aria-label="Edit form" title="Edit form">Edit Form</span>
                            </a>

                            <a t-if="form.state == 'COMPLETE'" role="button" class="btn btn-sm btn-info"
                               t-attf-href="/my/formio/form/{{ form.uuid }}?{{ keep_query() }}">
                               <i class="fa fa-check"/> <span aria-label="View form" title="View form">View Form</span>
                            </a>
                            <a t-elif="form.state == 'CANCEL'" role="button" class="btn btn-sm btn-info"
                               t-attf-href="/my/formio/form/{{ form.uuid }}?{{ keep_query() }}">
                               <i class="fa fa-check"/> <span aria-label="View form" title="View form">View Form</span>
                            </a>

                            <a t-if="form.allow_copy and form.state == 'COMPLETE'" role="button" class="btn btn-sm btn-dark"
                               t-attf-href="/my/formio/form/{{ form.uuid }}/copy?{{ keep_query() }}">
                               <i class="fa fa-copy"/> <span aria-label="Copy form" title="Copy form">Copy Form</span>
                            </a>

                            <a t-if="form.allow_unlink" role="button" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')"
                               t-attf-href="/my/formio/form/{{ form.uuid }}/delete?{{ keep_query() }}">
                               <i class="fa fa-trash"/> <span aria-label="Delete form" title="Delete form">Delete Form</span>
                            </a>

                            <a t-elif="not form.allow_unlink and form.state in ['PENDING', 'DRAFT']" role="button" class="btn btn-sm btn-warning"
                               t-attf-href="/my/formio/form/{{ form.uuid }}/cancel?{{ keep_query() }}">
                               <i class="fa fa-cross"/> <span aria-label="Cancel &amp; close Form" title="Cancel &amp; close Form" confirm="Are you sure?">Cancel Form</span>
                            </a>
                        </td>
                    </tr>
                </tbody>
            </t>
        </t>
    </template>

    <!-- portal edit -->

    <template id="portal_my_formio_edit" name="Portal Form" priority="40">
        <t t-call="portal.portal_layout">
            <div t-attf-class="formio_form_container mt16 {{ form.state }}">
                <t t-call="formio.form_iframe">
                    <t t-set="src" t-value="'/formio/portal/form/' + form.uuid"/>
                    <t t-set="formio_builder" t-value="form.builder_id"/>
                </t>
            </div>
        </t>
    </template>

    <!-- portal new -->

    <template id="portal_my_formio_new" name="Portal Form" priority="40">
        <t t-call="portal.portal_layout">
            <div t-attf-class="formio_form_container mt16">
                <t t-call="formio.form_iframe">
                    <t t-set="src" t-value="'/formio/portal/form/new/' + builder.name"/>
                    <t t-set="formio_builder" t-value="builder"/>
                </t>
            </div>
        </t>
    </template>

    <template id="formio_form_portal_new_embed" name="formio.form - portal new - embed">
        &lt;!DOCTYPE html&gt;
        <html>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>

                <script type="text/javascript" src="/formio/static/lib/iframe-resizer/iframeResizer.contentWindow.min.js"></script>
                <script type="text/javascript" src="/web/static/lib/jquery/jquery.js"></script>
                <script type="text/javascript" src="/web/static/lib/owl/owl.js"></script>

                <script type="module" t-attf-src="/formio/static/src/js/form/portal_new_app.js?{{ uuid }}"/>

                <t t-foreach="formio_css_assets" t-as="css">
                    <link rel="stylesheet" type="text/css" t-att-href="css.url"/>
                </t>
                <t t-foreach="formio_js_assets" t-as="js">
                    <script type="text/javascript" t-att-src="js.url"></script>
                </t>
                <link rel="stylesheet" t-attf-href="/formio/static/src/css/formio_form_embed.css?{{ uuid }}"/>
            </head>
            <body data-iframe-height="1">
                <t t-if="builder">
                    <h3 t-if="builder.show_form_title" class="formio_form_title"><span name="title" t-out="builder.title"/></h3>
                </t>

                <t t-if="len(form_languages) > 1">
                    <div class="formio_languages">
                        <t t-foreach="form_languages" t-as="lang">
                            <button class="btn btn-sm btn-default" t-att-lang="lang.formio_ietf_code" t-attf-onclick="setLanguage('{{ lang.formio_ietf_code }}', this)">
                                <span t-out="lang.name"/>
                            </button>
                        </t>
                    </div>
                </t>

                <div id="formio_form_server_error" class="alert alert-danger d-none">
                    <span id="formio_form_server_error_message"/>
                    <pre id="formio_form_server_error_traceback" class="d-none mt-4"/>
                </div>

                <!-- form shall be mounted here -->
                <div id="formio_form_app">
                    <div id="formio_form_loading_overlay"></div>
                </div>

                <t t-if="formio_builder_uuid">
                    <input type="hidden" id="formio_builder_uuid" name="formio_builder_uuid" t-att-value="formio_builder_uuid"/>
                </t>
            </body>
        </html>
    </template>
</odoo>
