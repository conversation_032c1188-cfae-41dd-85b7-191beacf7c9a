# Part of Odoo. See LICENSE file for full copyright and licensing details.

import json
import logging


from odoo import _, fields, models


from odoo.addons.payment_fedapay import const


_logger = logging.getLogger(__name__)


class PaymentProvider(models.Model):
    _inherit = 'payment.provider'

    code = fields.Selection(
        selection_add=[('fedapay', "FedaPay")], ondelete={'fedapay': 'set default'}
    )
    fedapay_email_account = fields.Char(
        string="Email",
        help="The public business email solely used to identify the account with FedaPay",
        required_if_provider='fedapay',
        default=lambda self: self.env.company.email,
    )
    fedapay_client_id = fields.Char(string="FedaPay Client ID")
    fedapay_public_key = fields.Char(
        string="FedaPay Public Key",
        required_if_provider='fedapay',
        groups='base.group_system'
    )
    fedapay_client_secret = fields.Char(string="FedaPay Client Secret", groups='base.group_system')

    fedapay_webhook_id = fields.Char(string="FedaPay Webhook ID")

    # === ACTION METHODS === #

    def action_fedapay_create_webhook(self):
        """ Create a new webhook.

        Note: This action only works for instances using a public URL.

        :return: None
        :raise UserError: If the base URL is not in HTTPS.
        """
    

    # #=== BUSINESS METHODS ===#

 

    # === BUSINESS METHODS - GETTERS === #

    def _get_supported_currencies(self):
        """ Override of `payment` to return the supported currencies. """
        supported_currencies = super()._get_supported_currencies()
        if self.code == 'fedapay':
            supported_currencies = supported_currencies.filtered(
                lambda c: c.name in const.SUPPORTED_CURRENCIES
            )
        return supported_currencies

    def _fedapay_get_api_url(self):
        """ Return the API URL according to the provider state.

        Note: self.ensure_one()

        :return: The API URL
        :rtype: str
        """
        self.ensure_one()

        if self.state == 'enabled':
            return 'https://api-m.fedapy.com'
        else:
            return 'https://api-m.sandbox.paypal.com'

    def _get_default_payment_method_codes(self):
        """ Override of `payment` to return the default payment method codes. """
        default_codes = super()._get_default_payment_method_codes()
        if self.code != 'fedapay':
            return default_codes
        return const.DEFAULT_PAYMENT_METHOD_CODES

    def _fedapay_get_inline_form_values(self, currency=None):
        """ Return a serialized JSON of the required values to render the inline form.

        Note: `self.ensure_one()`

        :param res.currency currency: The transaction currency.
        :return: The JSON serial of the required values to render the inline form.
        :rtype: str
        """
        inline_form_values = {
            'provider_id': self.id,
            'client_id': self.fedapay_client_id,
            'fedapay_public_key': self.fedapay_public_key,
            'currency_code': currency and currency.name,
        }
        return json.dumps(inline_form_values)
