from odoo import http
from odoo.http import request
from odoo.exceptions import ValidationError
import json
import logging

_logger = logging.getLogger(__name__)

class FedapayController(http.Controller):
    _complete_url = '/payment/fedapay/return'

    @http.route(_complete_url, type='http', auth='public', methods=['GET', 'POST'], csrf=False)
    def fedapay_return(self, **post):
        """ Handle the return from Fedapay after payment """
        _logger.info("Received POST data from Fedapay: %s", post)

        try:
            data = json.loads(post.get('data') or '{}')
            transaction = data.get('transaction', {})
            tx_sudo = request.env['payment.transaction'].sudo()._get_tx_from_notification_data('fedapay', data)
            tx_sudo._handle_notification_data('fedapay', data)
        except Exception as e:
            _logger.exception("Failed to process Fedapay return: %s", str(e))
            raise ValidationError("Fedapay: Failed to process payment response.")

        return request.redirect('/payment/status')