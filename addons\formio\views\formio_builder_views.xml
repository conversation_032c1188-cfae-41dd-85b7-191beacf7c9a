<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (https://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <record id="view_formio_builder_tree" model="ir.ui.view">
        <field name="name">formio.builder.list</field>
        <field name="model">formio.builder</field>
        <field name="arch" type="xml">
            <list string="Form Builders" default_order="id desc"
                  decoration-success="state in ('CURRENT')"
                  decoration-muted="state in ('OBSOLETE')">
                <field name="id" optional="hide"/>
                <field name="uuid" optional="hide"/>
                <field name="current_uuid" optional="hide"/>
                <field name="title"/>
                <field name="name"/>
                <field name="version"/>
                <field name="state"/>
                <field name="is_locked" optional="show"/>
                <field name="auto_save" optional="show"/>
                <field name="formio_res_model_id" optional="show"/>
                <field name="portal" optional="show"/>
                <field name="portal_url" optional="hide"/>
                <field name="public" optional="show"/>
                <field name="public_access_rule_type" optional="hide"/>
                <field name="public_url" optional="hide"/>
                <field name="formio_version_id"/>
                <field name="parent_id" optional="hide"/>
                <field name="write_date" optional="hide"/>
                <field name="create_date" optional="hide"/>
                <field name="forms" optional="show"/>
                <field name="active" column_invisible="True"/>
            </list>
        </field>
    </record>

    <record id="view_formio_builder_formio" model="ir.ui.view">
        <field name="name">formio.builder.formio</field>
        <field name="model">formio.builder</field>
        <field name="arch" type="xml">
            <formio_builder string="formio Builder">
                <field name="id"/>
                <field name="name"/>
                <field name="title"/>
                <field name="version"/>
                <field name="parent_version"/>
                <field name="formio_version_name"/>
                <field name="auto_save"/>
                <field name="is_locked"/>
                <field name="state"/>
                <field name="display_state"/>
            </formio_builder>
        </field>
    </record>

    <record id="view_formio_builder_form" model="ir.ui.view">
        <field name="name">formio.builder.form</field>
        <field name="model">formio.builder</field>
        <field name="arch" type="xml">
	    <form string="Form Builders">
                <header>
                    <button
                        name="action_current" type="object" string="Current"
                        invisible="state in ['CURRENT', 'OBSOLETE']"
                        confirm="Are you sure? This makes it possible to create and use these forms."
                        class="btn-primary"/>
                    <button
                        name="action_draft" type="object" string="Draft"
                        invisible="state == 'DRAFT'"
                        confirm="Are you sure? Modifying the Form Builder could cause existing forms to be invalid."/>
                    <button
                        name="action_obsolete" type="object" string="Obsolete"
                        invisible="state == 'OBSOLETE'"
                        confirm="Are you sure? The Form shall be unpublished."/>
                    <button
                        name="action_new_builder_version" type="object" string="Create New Version"
                        invisible="id == False"
                        confirm="Are you sure? This creates a new (draft) version."/>
                    <button
                        name="action_lock" type="object" string="Lock"
                        invisible="is_locked == True or id == False or state != 'CURRENT'"
                        confirm="Are you sure? No further modifications are possible in the Form Builder and configuration."
                        class="btn-primary"/>
                    <button
                        name="action_unlock" type="object" string="Unlock"
                        invisible="is_locked == False or id == False or state != 'CURRENT'"
                        confirm="Are you sure? Modifying the Form Builder could cause existing forms to be invalid."
                        class="btn-primary"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_formio"
                                type="object"
                                string="Form Builder"
                                aria-label="Form Builder"
                                groups="formio.group_formio_admin"
                                class="oe_stat_button bg-primary formio_builder"
                                icon="fa-rocket">
                            <div class="o_stat_info o_field_widget">
                                <span>Form Builder</span>
                            </div>
                        </button>
                        <button name="%(action_formio_builder_translation)d"
                                type="action"
                                context="{'default_builder_id': id, 'search_default_builder_id': id}"
                                class="oe_stat_button"
                                groups="formio.group_formio_admin"
                                icon="fa-language">
                            <field string="Translations" name="translations_count" widget="statinfo"/>
                        </button>
                        <button name="action_view_server_actions"
                                type="object"
                                class="oe_stat_button"
                                groups="base.group_system"
                                icon="fa-cog">
                            <field string="Server Actions" name="server_action_count" widget="statinfo"/>
                        </button>
                        <button name="action_view_forms"
                                type="object"
                                class="oe_stat_button"
                                context="{'default_builder_id': id}"
                                groups="formio.group_formio_admin" icon="fa-file-text">
                            <field string="Forms" name="forms_count" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title mb24">
                        <h1>
                            <field name="title" class="text-break" placeholder="Insert a meaningful title here"/>
                        </h1>
                    </div>
                    <group>
                        <div colspan="2" class="alert alert-info pt-3 pb-3" role="alert" invisible="id == False or is_schema_empty == False">
                            <i class="fa fa-info-circle" title="info"/> Start building a form by clicking the button <i class="fa fa-rocket"/> Form Builder
                        </div>
                        <group>
                            <field name="name"
                                   placeholder="Use ASCII letters, digits (0-9), - or _"
                                   readonly="is_locked == True"/>
                            <field name="version"/>
                            <field name="version_comment" required="version > 1"/>
                            <field name="parent_id" string="Parent Version"/>
                            <field name="active" invisible="1"/>
                            <field name="formio_form_model_id" invisible="True"/>
                            <field name="is_schema_empty" invisible="True"/>
                        </group>
                        <group>
                            <field name="uuid" invisible="id == False"/>
                            <field name="current_uuid" invisible="id == False"/>
                            <field name="is_locked" invisible="state != 'CURRENT'" widget="boolean_toggle"/>
                            <field name="auto_save" widget="boolean_toggle"/>
                            <field name="backend_use_draft" invisible="state != 'DRAFT'" widget="boolean_toggle"/>
                            <field name="backend_use_obsolete" invisible="state != 'OBSOLETE'" widget="boolean_toggle"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="General" name="settings">
                            <group name="settings">
                                <group name="formiojs_settings" string="formio.js">
                                    <field name="formio_version_id"
                                           readonly="is_locked == True"
                                           options="{'no_create_edit': True, 'no_quick_create': True}"/>
                                    <div colspan="2" invisible="formio_version_is_dummy == True">
                                        <i class="fa fa-info-circle" title="info"/> Download and install formio.js versions (menu: Configuration / formio.js versions)
                                    </div>
                                    <field name="formio_version_is_dummy" invisible="1"/>
                                    <div colspan="2" invisible="formio_version_is_dummy == False">
                                        <strong class="text-danger">
                                            <i class="fa fa-info-circle" title="info"/> Replace this "Dummy" version, which doesn't work<br/>
                                            <i class="fa fa-info-circle" title="info"/> Dummy version is only here to bootstrap (default) demo data<br/>
                                            <i class="fa fa-info-circle" title="info"/> Download and install a formio.js version (menu: Configuration / formio.js versions)
                                        </strong>
                                    </div>
                                </group>
                                <group name="publish_settings" string="Publish">
                                    <field name="portal" string="Publish on Portal"/>
                                    <field name="public" string="Publish to Public"/>
                                    <div class="text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Configure in related tabs (Portal, Public / Website)
                                    </div>
                                </group>
                                <group name="wizard_settings" string="Wizard">
                                    <field name="wizard" string="Wizard" groups="formio.group_formio_admin"
                                           readonly="is_locked == True"/>
                                    <field name="wizard_on_change_page_save_draft" string="Save Draft on Change Page" groups="formio.group_formio_admin"
                                           readonly="is_locked == True"/>
                                    <div class="text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Multi-page form, with previous/next page buttons
                                    </div>
                                </group>
                                <group name="integration_settings" string="Integration">
                                    <field name="formio_res_model_id"
                                           readonly="is_locked == True"
                                           edit="false" create="false" quick_create="false"
                                           options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"/>
                                    <field name="res_model" invisible="formio_res_model_id == False"/>
                                    <div class="text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Configure in Resource tab (if settings are available)
                                    </div>
                                    <div class="text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Not applicable with public/website Forms and custom implementations (depends).
                                    </div>
                                </group>
                                <group name="debug_mode" string="Debug Mode">
                                    <field name="debug" widget="boolean_toggle"/>
                                </group>
                                <group name="other_settings" string="Other">
                                    <field name="backend_submission_url_add_query_params_from" readonly="is_locked == True"/>
                                    <div class="text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Enables adding the URL query params from the window's iframe (src) or window.parent to the form's submission (load) URL endpoint.
                                        <i class="fa fa-info-circle" title="info"/> Similar setting for portal and public/website forms (see tabs).
                                    </div>
                                </group>
                                <separator/>
                                <field name="description" placeholder="Description..." groups="formio.group_formio_admin"/>
                            </group>
                        </page>
                        <page string="Resource" name="res_model_settings" invisible="formio_res_model_id == False"/>
                        <page string="Portal" name="portal_settings">
                            <group>
                                <group name="portal_urls" colspan="2" invisible="portal_url == False">
                                    <field name="portal_url" widget="CopyClipboardChar" style="font-size: 1.0em;"/>
                                </group>
                                <group name="portal_redirect_after_save_draft" string="Redirect After Save Draft">
                                    <field name="portal_save_draft_done_url" groups="formio.group_formio_admin" string="Redirect URL" readonly="0"/>
                                </group>
                                <group name="portal_redirect_after_submit" string="Redirect After Submit">
                                    <field name="portal_submit_done_url" groups="formio.group_formio_admin" string="Redirect URL" readonly="0"/>
                                </group>
                                <group name="misc" string="Miscellaneous">
                                    <field name="portal_submission_url_add_query_params_from" string="Add Query Params to Submission URL from" readonly="is_locked == True"/>
                                    <div class="text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Enables adding the URL query params from the window's iframe (src) or window.parent to the form's submission (load) URL endpoint.
                                    </div>
                                    <field name="portal_scroll_into_view_selector"
                                           string="Scroll Into View Selector"
                                           groups="formio.group_formio_admin"
                                           readonly="is_locked == True"/>
                                </group>

                            </group>
                        </page>
                        <page string="Public / Website" name="public_settings">
                            <div class="text-muted mb-4" colspan="2">
                                <i class="fa fa-info-circle" title="info"/> Configuration of Website features shall also be done here
                            </div>
                            <group>
                                <group name="public_current_url" colspan="2" invisible="public_current_url == False">
                                    <field name="public_current_url" widget="CopyClipboardChar" style="font-size: 1.0em;"/>
                                    <div class="text-muted mb-4" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Public Current URL: Public Form with versioning (same Name) in state Current
                                    </div>
                                </group>
                                <group name="public_url" colspan="2" invisible="public_url == False">
                                    <field name="public_url" widget="CopyClipboardChar" style="font-size: 1.0em;"/>
                                    <div class="text-muted mb-4" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Public URL: Public Form for this form builder version in state Current
                                    </div>
                                </group>
                                <group name="general" string="General">
                                    <field name="public_access_rule_type" required="public == True"/>
                                    <div class="text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Other <em>Public Access Rule Type(s)</em> can be implemented by other modules.
                                    </div>
                                    <label for="public_access_interval_number" string="Expire After" invisible="public_access_rule_type != 'time_interval'"/>
                                    <div invisible="public_access_rule_type != 'time_interval'">
                                        <field name="public_access_interval_number" class="oe_inline" groups="formio.group_formio_admin" required="public == True"/>
                                        <field name="public_access_interval_type" class="oe_inline ml-2" groups="formio.group_formio_admin" required="public == True"/>
                                    </div>
                                    <div class="text-muted" colspan="2" invisible="public_access_rule_type != 'time_interval'">
                                        <i class="fa fa-info-circle" title="info"/> Expire After: Applicable on the Form it's (datetime field) Public Access From.
                                    </div>
                                </group>
                                <group name="public_redirect_after_save_draft" string="Redirect After Save Draft">
                                    <field name="public_save_draft_done_url" groups="formio.group_formio_admin" string="Redirect URL" readonly="0"/>
                                </group>
                                <group name="public_redirect_after_submit" string="Redirect After Submit">
                                    <field name="public_submit_done_url" groups="formio.group_formio_admin" string="Redirect URL" readonly="0"/>
                                </group>
                                <group name="misc" string="Miscellaneous">
                                    <field name="public_submission_url_add_query_params_from" string="Add Query Params to Submission URL from" readonly="is_locked == True"/>
                                    <div class="text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Enables adding the URL query params from the window's iframe (src) or window.parent to the form's submission (load) URL endpoint.
                                    </div>
                                    <field name="public_scroll_into_view_selector"
                                           string="Scroll Into View Selector"
                                           groups="formio.group_formio_admin"
                                           readonly="is_locked == True"/>
                                </group>
                            </group>
                        </page>
                        <page string="Translations" name="translations" groups="formio.group_formio_admin">
                            <group>
                                <field name="language_en_enable"/>
                            </group>
                            <group string="Translations" name="translations">
                                <div class="text-muted">
                                    <i class="fa fa-info-circle" title="info"/> Translations for form components (labels, placeholders etc).<br/>
                                    <i class="fa fa-info-circle" title="info"/> These translations take precedence over all other translations (formio.js translations, model translations).
                                </div>
                                <field name="translations" nolabel="1" colspan="2" context="{'default_builder_id': id}">
                                    <list editable="bottom">
                                        <field name="builder_id" column_invisible="True"/>
                                        <field name="lang_id"/>
                                        <field name="source_property" placeholder="A formio.js library translation property ..." optional="show"/>
                                        <field name="source"/>
                                        <field name="value"/>
                                    </list>
                                </field>
                            </group>
                            <group string="Model Translations" name="model">
                                <div class="text-muted">
                                    <i class="fa fa-info-circle" title="info"/> Register for which Language and Model the translations should be registered and used in the form.
                                </div>
                                <field name="translation_model_ids" nolabel="1" colspan="2" context="{'default_builder_id': id}">
                                    <list editable="bottom">
                                        <field name="builder_id" column_invisible="True"/>
                                        <field name="lang_id"/>
                                        <field name="model_id"/>
                                    </list>
                                </field>
                            </group>
                            <group string="Languages &amp; Locales" name="languages_locales">
                                <field name="languages" nolabel="1" colspan="2">
                                    <list editable="bottom">
                                        <field name="name"/>
                                        <field name="code" optional="show"/>
                                        <field name="formio_ietf_code" optional="hide"/>
                                        <field name="formio_short_code" optional="show"/>
                                        <field name="active"/>
                                    </list>
                                </field>
                            </group>
                            <div class="text-muted">
                                <i class="fa fa-info-circle" title="info"/> Translations of the <strong>formio.js version</strong> can be done via the General tab (click field: formio.js version).<br/>
                                <i class="fa fa-info-circle" title="info"/> The button "Translations" has the same as in this list.
                            </div>
                        </page>
                        <page string="Form Display" name="form_display_settings">
                            <group>
                                <group string="General" name="general">
                                    <field name="full_width"/>
                                </group>
                                <group string="iFrame Embedding" name="iframe_embed">
                                    <field name="iframe_resizer_body_margin" groups="formio.group_formio_admin"/>
                                </group>
                                <group string="Info" name="info">
                                    <field name="show_form_title" string="Show Title" groups="formio.group_formio_admin" readonly="is_locked == True"/>
                                    <field name="show_form_state" string="Show State" groups="formio.group_formio_admin" readonly="is_locked == True"/>
                                    <field name="show_form_user_metadata" groups="formio.group_formio_admin" readonly="is_locked == True"/>
                                    <field name="show_form_id" string="Show ID" groups="formio.group_formio_admin" readonly="is_locked == True"/>
                                    <field name="show_form_uuid" string="Show UUID" groups="formio.group_formio_admin" readonly="is_locked == True"/>
                                </group>
                                <group string="Submission" name="submission">
                                    <field name="view_as_html" groups="formio.group_formio_admin"/>
                                </group>
                            </group>
                        </page>
                        <page string="Form Permissions" name="form_permissions" groups="formio.group_formio_admin">
                            <div class="mb-3 text-muted">
                                <i class="fa fa-info-circle" title="info"/> Specific permissions on Forms
                            </div>
                            <group>
                                <field name="allow_force_update_state_group_ids" />
                            </group>
                            <group name="actions_settings" string="Actions">
                                <field name="form_allow_copy" groups="formio.group_formio_admin" />
                                <field name="form_copy_to_current" groups="formio.group_formio_admin" invisible="form_allow_copy == False"/>
                            </group>
                        </page>
                        <page string="Components API" name="formio_components_api">
                            <group name="main"/>
                            <group string="Partner" name="partner" colspan="2">
                                <div class="text-muted mb-2" colspan="2">
                                    <i class="fa fa-info-circle" title="info"/> To create or link (existing) Partner with a Form submission, specify the fields (Email, Name) from the Components API / Property Names.<br/>
                                    Partner determination/match shall be done by Email. This API is especially useful for public Forms.
                                </div>
                                <group>
                                    <field name="component_partner_email" string="Email" readonly="is_locked == True"/>
                                    <field name="component_partner_name" string="Name" readonly="is_locked == True"/>
                                </group>
                                <group>
                                    <field name="component_partner_add_follower" string="Add to Followers" readonly="is_locked == True"/>
                                    <field name="component_partner_activity_user_id" string="Activity User" readonly="is_locked == True"/>
                                </group>
                            </group>
                        </page>
                        <page string="Actions API" name="actions_api" groups="formio.group_formio_admin">
                            <group>
                                <group string="Server Actions" name="server_actions" colspan="2">
                                    <div class="mb-2 text-muted" colspan="2">
                                        <i class="fa fa-info-circle" title="info"/> Server Actions which process the form submission.<br/>
                                        <i class="fa fa-info-circle" title="info"/> Users with access are in the group "Administration / Settings", then the actions are available here.<br/>
                                        <i class="fa fa-info-circle" title="info"/> Be careful about ordering the sequence if a server action is linked to other Form Builders.
                                    </div>
                                    <field name="server_action_ids"
                                           groups="base.group_system"
                                           context="{'default_model_id': formio_form_model_id, 'default_formio_form_execute_after_action': 'submit', 'default_state': 'code'}"
                                           nolabel="1"
                                           colspan="2"
                                           readonly="is_locked == True">
                                        <list>
                                            <field name="sequence" widget="handle"/>
                                            <field name="name"/>
                                            <field name="formio_form_execute_after_action" string="Execute After"/>
                                            <field name="model_id" optional="hide"/>
                                            <field name="state"/>
                                            <field name="formio_builder_ids" widget="many2many_tags" string="Form Builders" optional="show"/>
                                            <field name="formio_ref" optional="hide"/>
                                        </list>
                                    </field>
                                </group>
                            </group>
                        </page>
                        <page string="JS Options" name="javascript_options" groups="formio.group_formio_admin">
                            <div class="text-muted mt-4">
                                <i class="fa fa-info-circle" title="info"/> Documentation<br/>
                                For example, Options could contain the Form Builder editForm with some File component settings:<br/>
                                <a href="https://github.com/formio/formio.js/blob/master/src/components/file/editForm/File.edit.file.js" target="_blank">
                                    https://github.com/formio/formio.js/blob/master/src/components/file/editForm/File.edit.file.js
                                </a>
                            </div>
                            <div>
                                <pre style="background-color: #e9ecef;">{
  'editForm': {
    'file': [
      {
        'key': 'file',
        'components': [
          {'key': 'webcam', 'defaultValue': True},
          {'key': 'storage', 'defaultValue': 'base64'}
        ]
      }
    ]
  }
}</pre>
                            </div>
                            <div>
                                <group>
                                    <field
                                        name="formio_js_options_id"
                                        string="Select formio.js Options"
                                        readonly="is_locked == True"
                                        placeholder="Select from predefined ..."/>
                                </group>
                            </div>
                            <field name="formio_js_options"
                                   placeholder="{ ... }"
                                   readonly="is_locked == True"
                                   widget="ace"
                                   options="{'mode': 'js'}"/>
                        </page>
                        <page string="JSON Schema" name="schema" groups="formio.group_formio_admin">
                            <field name="schema" readonly="is_locked == True"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="view_formio_builder_search" model="ir.ui.view">
        <field name="name">formio.builder.search</field>
        <field name="model">formio.builder</field>
        <field name="arch" type="xml">
            <search string="Form Builders">
                <field name="title"/>
                <field name="name"/>
                <field name="uuid"/>
                <field name="current_uuid"/>
                <field name="formio_version_id"/>
                <separator/>
                <filter string="Draft" name="state_draft"
                        domain="[('state', '=', 'DRAFT')]"/>
                <filter string="Current" name="state_current"
                        domain="[('state', '=', 'CURRENT')]"/>
                <filter string="Obsolete" name="state_obsolete"
                        domain="[('state', '=', 'OBSOLETE')]"/>
                <separator/>
                <filter string="Portal" name="portal"
                        domain="[('portal', '=', True)]"/>
                <filter string="Public" name="public"
                        domain="[('public', '=', True)]"/>
                <separator/>
                <filter string="Wizard" name="wizard"
                        domain="[('wizard', '=', True)]"/>
                <filter string="Not Wizard" name="not_portal"
                        domain="[('wizard', '=', False)]"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Name" name="name" domain="[]" context="{'group_by':'name'}"/>
                    <filter string="State" name="state" domain="[]" context="{'group_by':'state'}"/>
                    <filter string="Current UUID" name="current_uuid" domain="[]" context="{'group_by':'current_uuid'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- actions -->
    <record id="action_formio_builder" model="ir.actions.act_window">
        <field name="name">Form Builders</field>
        <field name="res_model">formio.builder</field>
        <field name="view_mode">list,form,formio_builder</field>
    </record>

    <record id="action_formio_builder_view_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="0"/>
        <field name="view_mode">list</field>
        <field name="view_id" ref="formio.view_formio_builder_tree"/>
        <field name="act_window_id" ref="action_formio_builder"/>
    </record>
    <record id="action_formio_builder_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="formio.view_formio_builder_form"/>
        <field name="act_window_id" ref="action_formio_builder"/>
    </record>
    <record id="action_formio_builder_view_formio" model="ir.actions.act_window.view">
        <field name="sequence" eval="2"/>
        <field name="view_mode">formio_builder</field>
        <field name="view_id" ref="formio.view_formio_builder_formio"/>
        <field name="act_window_id" ref="action_formio_builder"/>
    </record>
</odoo>
