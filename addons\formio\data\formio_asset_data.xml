<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <data>
        <record id="css_bootstrap_4_1_3" model="ir.attachment">
            <field name="name">bootstrap 4.1.3 bootstrap.min.css</field>
            <field name="type">url</field>
            <field name="res_model">formio.version.asset</field>
            <field name="public" eval="True"/>
            <field name="url">/formio/static/lib/bootstrap/4.1.3/bootstrap.min.css</field>
        </record>

        <record id="css_bootstrap_4_4_1" model="ir.attachment">
            <field name="name">bootstrap 4.4.1 bootstrap.min.css</field>
            <field name="type">url</field>
            <field name="res_model">formio.version.asset</field>
            <field name="public" eval="True"/>
            <field name="url">/formio/static/lib/bootstrap/4.4.1/bootstrap.min.css</field>
        </record>

        <record id="css_bootstrap_4_6_2" model="ir.attachment">
            <field name="name">bootstrap 4.6.2 bootstrap.min.css</field>
            <field name="type">url</field>
            <field name="res_model">formio.version.asset</field>
            <field name="public" eval="True"/>
            <field name="url">/formio/static/lib/bootstrap/4.6.2/bootstrap.min.css</field>
        </record>

        <record id="css_bootstrap_5_2_3" model="ir.attachment">
            <field name="name">bootstrap 5.2.3 bootstrap.min.css</field>
            <field name="type">url</field>
            <field name="res_model">formio.version.asset</field>
            <field name="public" eval="True"/>
            <field name="url">/formio/static/lib/bootstrap/5.2.3/bootstrap.min.css</field>
        </record>

        <record id="css_bootstrap_rtl_5_2_3" model="ir.attachment">
            <field name="name">bootstrap 5.2.3 bootstrap.rtl.min.css</field>
            <field name="type">url</field>
            <field name="res_model">formio.version.asset</field>
            <field name="public" eval="True"/>
            <field name="url">/formio/static/lib/bootstrap/5.2.3/bootstrap.rtl.min.css</field>
        </record>
    </data>
</odoo>
