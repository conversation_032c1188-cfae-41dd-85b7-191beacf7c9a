=========================
Keycloak auth integration
=========================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:9cdba0b2846e4d81efc52ed2c8dd4ce399444d8eaf4bb9ced1828fdc3c546a21
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fserver--auth-lightgray.png?logo=github
    :target: https://github.com/OCA/server-auth/tree/11.0/auth_keycloak
    :alt: OCA/server-auth
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/server-auth-11-0/server-auth-11-0-auth_keycloak
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/server-auth&target_branch=11.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module adds support for SSO authentication via `Keycloak <https://www.keycloak.org/>`_

**Table of contents**

.. contents::
   :local:

Configuration
=============

Settings -> Users -> OAuth Providers -> Keycloak

Adjust endpoints according to your setup.

Enable it: tick "Allowed".

Official docs: https://www.keycloak.org/docs


.. note:: You must make sure your settings are correct.
   Testing scripts are provided by this module in the folder `examples`.

   Please follow instructions contained in its README.

Usage
=====

Frontend
~~~~~~~~

When the provider is enabled you'll see an extra login button on login form.
Click on it to get redirected to Keycloak.

Backend
~~~~~~~

**Link existing users from Keycloak**

If you have existing users in Odoo and they are not linked to Keycloak yet
you can:

1. get back to Settings -> Users -> OAuth Providers -> Keycloak
2. configure "Users management" box
3. click on "Sync users" button
4. select the matching key
5. submit

Once the it's done all matching and updated users will be listed in a list view.
Now your users will be able to log in on Keycloak


**Push new users to Keycloak**

Usually Keycloak is already populated w/ your users base.
Many times this will come via LDAP, AD, pick yours.

Still, you might need to push some users to Keycloak on demand,
maybe just for testing.

If you need this, either you

1. go to a single user form
2. hit the button "Push to Keycloak" (in the header)
3. use the wizard to push it

or

1. go to the users list view
2. select some users
3. click on Actions -> Push to Keycloak
4. select "Keycloak" provider
5. push them all

Changelog
=========

10.0.1.0.0 2018-10-17
~~~~~~~~~~~~~~~~~~~~~

* Initial implementation

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/server-auth/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/server-auth/issues/new?body=module:%20auth_keycloak%0Aversion:%2011.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Camptocamp

Contributors
~~~~~~~~~~~~

* Simone Orsi <<EMAIL>>
* Dennis Sluijk <<EMAIL>>

Other credits
~~~~~~~~~~~~~

Development sponsored by `Sensefly <https://www.sensefly.com/>`_ and `UTB <http://www.utb.fr/>`_.

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/server-auth <https://github.com/OCA/server-auth/tree/11.0/auth_keycloak>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
