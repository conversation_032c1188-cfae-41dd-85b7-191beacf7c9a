# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_keycloak
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2018-12-12 19:58+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 3.3\n"

#. module: auth_keycloak
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_create_wiz_pwd
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_sync_mixin_pwd
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_sync_wiz_pwd
#: model:ir.model.fields,help:auth_keycloak.field_auth_oauth_provider_superuser_pwd
msgid "\"Superuser\" user password"
msgstr "Mot de passe du \"super utilisateur\""

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_create_wiz
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_sync_wiz
msgid ""
"<strong>Users managerment not enabled!</strong>\n"
"                You must configure \"Users management\" parameters on "
"selected provider."
msgstr ""
"<strong>La gestion des utilisateurs n'est pas activée !</strong>\n"
"                Vous devez paramétrer la « gestion des utilisateurs » sur le "
"fournisseur sélectionné."

#. module: auth_keycloak
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_create_wiz_user
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_sync_mixin_user
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_sync_wiz_user
#: model:ir.model.fields,help:auth_keycloak.field_auth_oauth_provider_superuser
msgid "A super power user that is able to CRUD users on KC."
msgstr ""
"Un utilisateur avec des droits avancés qui a la possibilité de Créer / "
"Modifier / Supprimer des utilisateurs sur Keycloak."

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_create_wiz
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_sync_wiz
msgid "Cancel"
msgstr "Annuler"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_oauth_provider_client_secret
#, fuzzy
msgid "Client Secret"
msgstr "Secret du client"

#. module: auth_keycloak
#: code:addons/auth_keycloak/wizard/keycloak_sync_wiz.py:208
#, python-format
msgid ""
"Conflict on user values. Please verify that all values supposed to be unique "
"are really unique. %(detail)s"
msgstr ""
"Conflit sur les valeurs de l'utilisateur. Vérifiez que toutes les valeurs "
"qui sont sensées être uniques le sont réellement. %(detail)s"

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_create_wiz
msgid "Create"
msgstr "Créer"

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.view_users_form
msgid "Create this user on Keycloak too."
msgstr "Créer cet utilisateur également sur Keycloak."

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_create_wiz
msgid "Create user on Keycloak"
msgstr "Créer l'utilisateur sur Keycloak"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_create_uid
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_create_uid
msgid "Created by"
msgstr "Créé par"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_create_date
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_create_date
msgid "Created on"
msgstr "Créé le"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_display_name
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin_display_name
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_id
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin_id
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_id
msgid "ID"
msgstr "ID"

#. module: auth_keycloak
#: code:addons/auth_keycloak/models/res_users.py:59
#, python-format
msgid "Keycloak provider not found or not configured properly."
msgstr "Fournisseur Keycloak non trouvé ou mal configuré."

#. module: auth_keycloak
#: model:ir.actions.act_window,name:auth_keycloak.keycloak_sync_users
msgid "Keycloak sync users"
msgstr "Keycloak synchronise des utilisateurs"

#. module: auth_keycloak
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_create_wiz_login_match_key
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_sync_mixin_login_match_key
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_sync_wiz_login_match_key
msgid "Keycloak user field to match users' login."
msgstr ""
"Champ utilisateur dans Keycloak correspondant au login de l'utilisateur."

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz___last_update
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin___last_update
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_write_uid
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_write_date
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: auth_keycloak
#: model:auth.oauth.provider,body:auth_keycloak.default_keycloak_provider
#, fuzzy
msgid "Log in with Keycloak"
msgstr "Clé correspondant au login"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_login_match_key
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin_login_match_key
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_login_match_key
#, fuzzy
msgid "Login Match Key"
msgstr "Clé correspondant au login"

#. module: auth_keycloak
#: code:addons/auth_keycloak/wizard/keycloak_sync_wiz.py:195
#, python-format
msgid "No user selected"
msgstr "Aucun utilisateur sélectionné"

#. module: auth_keycloak
#: model:ir.model,name:auth_keycloak.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr "Fournisseur OAuth2"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_provider_id
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin_provider_id
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_provider_id
msgid "Provider"
msgstr "Fournisseur"

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.view_users_form
msgid "Push to Keycloak"
msgstr "Pousser sur Keycloak"

#. module: auth_keycloak
#: model:ir.actions.act_window,name:auth_keycloak.keycloak_create_users
#, fuzzy
msgid "Push users to Keycloak"
msgstr "Pousser sur Keycloak"

#. module: auth_keycloak
#: code:addons/auth_keycloak/wizard/keycloak_sync_wiz.py:71
#, python-format
msgid "Something went wrong. Please check logs."
msgstr "Quelque chose s'est mal passé. Veuillez vérifier les logs."

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_user
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin_user
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_user
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_oauth_provider_superuser
msgid "Superuser"
msgstr "Super utilisateur"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_pwd
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin_pwd
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_pwd
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_oauth_provider_superuser_pwd
#, fuzzy
msgid "Superuser Pwd"
msgstr "Mot de passe du super utilisateur"

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_sync_wiz
msgid "Sychronize existing users"
msgstr "Synchroniser les utilisateurs existants"

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_sync_wiz
msgid "Sync"
msgstr "Synchroniser"

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.view_oauth_provider_form
msgid "Sync users"
msgstr "Synchroniser les utilisateurs"

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.auth_keycloak_sync_wiz
msgid "Synchronize users"
msgstr "Synchroniser les utilisateurs"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_user_ids
#, fuzzy
msgid "User"
msgstr "Utilisateurs"

#. module: auth_keycloak
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_create_wiz_endpoint
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_sync_mixin_endpoint
#: model:ir.model.fields,help:auth_keycloak.field_auth_keycloak_sync_wiz_endpoint
#: model:ir.model.fields,help:auth_keycloak.field_auth_oauth_provider_users_endpoint
msgid "User endpoint"
msgstr "Endpoint utilisateur"

#. module: auth_keycloak
#: model:ir.model,name:auth_keycloak.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_endpoint
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin_endpoint
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_endpoint
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_oauth_provider_users_endpoint
#, fuzzy
msgid "Users Endpoint"
msgstr "Endpoint utilisateurs"

#. module: auth_keycloak
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_create_wiz_management_enabled
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_mixin_management_enabled
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_keycloak_sync_wiz_management_enabled
#: model:ir.model.fields,field_description:auth_keycloak.field_auth_oauth_provider_users_management_enabled
#, fuzzy
msgid "Users Management Enabled"
msgstr "Gestion des utilisateurs activée"

#. module: auth_keycloak
#: model:ir.ui.view,arch_db:auth_keycloak.view_oauth_provider_form
msgid "Users management (Keycloak)"
msgstr "Gestion des utilisateurs (Keycloak)"

#. module: auth_keycloak
#: code:addons/auth_keycloak/wizard/keycloak_sync_wiz.py:57
#, python-format
msgid "Users management must be enabled on selected provider"
msgstr ""
"La gestion des utilisateurs doit être activée sur le fournisseur sélectionné"

#. module: auth_keycloak
#: model:ir.model,name:auth_keycloak.model_auth_keycloak_create_wiz
msgid "auth.keycloak.create.wiz"
msgstr "auth.keycloak.create.wiz"

#. module: auth_keycloak
#: model:ir.model,name:auth_keycloak.model_auth_keycloak_sync_mixin
msgid "auth.keycloak.sync.mixin"
msgstr "auth.keycloak.sync.mixin"

#. module: auth_keycloak
#: model:ir.model,name:auth_keycloak.model_auth_keycloak_sync_wiz
msgid "auth.keycloak.sync.wiz"
msgstr "auth.keycloak.sync.wiz"

#. module: auth_keycloak
#: selection:auth.keycloak.create.wiz,login_match_key:0
#: selection:auth.keycloak.sync.mixin,login_match_key:0
#: selection:auth.keycloak.sync.wiz,login_match_key:0
msgid "email"
msgstr "email"

#. module: auth_keycloak
#: selection:auth.keycloak.create.wiz,login_match_key:0
#: selection:auth.keycloak.sync.mixin,login_match_key:0
#: selection:auth.keycloak.sync.wiz,login_match_key:0
msgid "username"
msgstr "nom d'utilisateur"

#~ msgid "Keycloak create users"
#~ msgstr "Keycloak crée des utilisateurs"

#~ msgid "User ids"
#~ msgstr "Ids utilisateur"
