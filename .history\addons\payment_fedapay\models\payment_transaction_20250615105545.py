# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging
import pprint

from odoo import _, fields, models
from odoo.exceptions import ValidationError

from odoo.addons.payment import utils as payment_utils
from odoo.addons.payment_fedapay import utils as fedapay_utils
from odoo.addons.payment_fedapay.const import PAYMENT_STATUS_MAPPING

_logger = logging.getLogger(__name__)


class PaymentTransaction(models.Model):
    _inherit = 'payment.transaction'

    fedapay_type = fields.Char(string="PayPal Transaction Type")
    fedapay_transaction_id = fields.Char(string="Fedapay Transaction ID")
    fedapay_status = fields.Char(string="Fedapay Status")

    def _get_specific_processing_values(self, processing_values):
        res = super()._get_specific_processing_values(processing_values)
        reut
        # if self.provider_code != 'fedapay':
        #     return res

        # # Prepare transaction payload
        # payload = {
        #     'amount': float(self.amount),
        #     'description': f"Achat via Odoo - {self.reference}",
        #     'public_key': self.provider_id.fedapay_public_key,
        #     'callback_url': "",
        #     'customer_email': self.partner_email or '',
        #     'reference': self.reference,
        # }

        # # Optional metadata
        # if self.partner_name:
        #     payload['metadata'] = {
        #         'customer_name': self.partner_name,
        #     }

        # return {'fedapay_payload': payload}

    def _fedapay_prepare_order_payload(self):
        """ Prepare the payload for the FedaPay create order request.

        :return: The requested payload to create a FedaPay order.
        :rtype: dict
        """
        partner_first_name, partner_last_name = payment_utils.split_partner_name(self.partner_name)
        if self.partner_id.is_public:
            invoice_address_vals = {'address': {'country_code': self.company_id.country_code}}
            shipping_address_vals = {}
        else:
            invoice_address_vals = fedapay_utils.format_partner_address(self.partner_id)
            shipping_address_vals = fedapay_utils.format_shipping_address(self)
        shipping_preference = 'SET_PROVIDED_ADDRESS' if shipping_address_vals else 'NO_SHIPPING'

        payload = {
            'intent': 'CAPTURE',
            'purchase_units': [
                {
                    'reference_id': self.reference,
                    'description': f'{self.company_id.name}: {self.reference}',
                    'amount': {
                        'currency_code': self.currency_id.name,
                        'value': self.amount,
                    },
                    'payee':  {
                        'display_data': {
                            'brand_name': self.provider_id.company_id.name,
                        },
                        'email_address': "<EMAIL>",
                    },
                    **shipping_address_vals,
                },
            ],
            'payment_source': {
                'paypal': {
                    'experience_context': {
                        'shipping_preference': shipping_preference,
                    },
                    'name': {
                        'given_name': partner_first_name,
                        'surname': partner_last_name,
                    },
                    **invoice_address_vals,
                },
            },
        }
        # FedaPay does not accept None set to fields and to avoid users getting errors when email
        # is not set on company we will add it conditionally since its not a required field.
        if company_email := self.provider_id.company_id.email:
            payload['purchase_units'][0]['payee']['display_data']['business_email'] = company_email

        return payload
    
    def _get_tx_from_notification_data(self, provider_code, notification_data):
        tx = super()._get_tx_from_notification_data(provider_code, notification_data)
        if provider_code != 'fedapay' or len(tx) == 1:
            return tx

        reference = notification_data.get('reference')
        tx = self.search([('reference', '=', reference), ('provider_code', '=', 'fedapay')])
        if not tx:
            raise ValidationError(_("No transaction found matching reference %s.", reference))
        return tx

    def _process_notification_data(self, notification_data):
        super()._process_notification_data(notification_data)
        if self.provider_code != 'fedapay':
            return

        transaction = notification_data.get('transaction', {})
        self.fedapay_transaction_id = transaction.get('id')
        self.fedapay_status = transaction.get('status')

        status = transaction.get('status')
        reason = notification_data.get('reason')

        if status == 'approved':
            self._set_done()
        elif status == 'pending':
            self._set_pending(state_message=reason)
        elif status in ['canceled', 'declined']:
            self._set_canceled(state_message=reason)
        else:
            self._set_error("Fedapay: Unknown status received.")