<?xml version="1.0"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl.html) -->

<odoo>
    <data noupdate="1">
        <record id="ir_cron_formio_version_github_tag_check_and_register" forcecreate="True" model="ir.cron">
            <field name="name">Forms: Check and register new Versions (GitHub tags)</field>
            <field name="model_id" ref="model_formio_version_github_tag"/>
            <field name="state">code</field>
            <field name="code">model.check_and_register_available_versions()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
        </record>

        <record id="ir_cron_formio_license_notify_renewal_reminder_actitivies" forcecreate="True" model="ir.cron">
            <field name="name">Forms: Notify License Renewal Reminders</field>
            <field name="model_id" ref="model_formio_license"/>
            <field name="state">code</field>
            <field name="code">model.cron_notify_renewal_reminder_actitivies()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
        </record>
    </data>
</odoo>
