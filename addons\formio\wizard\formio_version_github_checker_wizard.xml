<?xml version="1.0" encoding='UTF-8'?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <record id="view_formio_version_github_checker_wizard" model="ir.ui.view">
        <field name="name">Formio Version GitHub Checker Wizard</field>
        <field name="model">formio.version.github.checker.wizard</field>
        <field name="arch" type="xml">
            <form string="formio (js) Version GitHub Importer">
                <sheet>
                    <group name="available_version_github_tags" string="Available Versions (releases)">
                        <field name="available_version_github_tag_ids" nolabel="1" colspan="2">
                            <list default_order="name desc" limit="15" create="0" edit="0" delete="0">
                                <field name="name"/>
                                <field name="changelog_url" widget="url"/>
                            </list>
                        </field>
                    </group>
                </sheet>
                <footer>
                    <button
                        name="action_register_available_versions"
                        string="Register Available Versions"
                        type="object"
                        class="btn btn-primary"
                        invisible="available_version_github_tag_ids == []"/>
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
