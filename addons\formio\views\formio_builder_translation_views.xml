<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <record id="view_formio_builder_translation_tree" model="ir.ui.view">
        <field name="name">formio.builder.translation.list</field>
        <field name="model">formio.builder.translation</field>
        <field name="arch" type="xml">
            <list string="Translations" editable="top">
                <field name="builder_id" optional="show"/>
                <field name="lang_id"/>
                <field name="source_property"/>
                <field name="source"/>
                <field name="value"/>
            </list>
        </field>
    </record>

    <record id="view_formio_builder_translation_form" model="ir.ui.view">
        <field name="name">formio.builder.translation.form</field>
        <field name="model">formio.builder.translation</field>
        <field name="arch" type="xml">
	    <form string="Translation">
                <sheet>
                    <group>
                        <field name="builder_id"/>
                        <field name="lang_id"/>
                        <field name="source_property" placeholder="A formio.js library translation property ..."/>
                        <field name="source"/>
                        <field name="value"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_formio_builder_translation_search" model="ir.ui.view">
        <field name="name">formio.builder.translation.search</field>
        <field name="model">formio.builder.translation</field>
        <field name="arch" type="xml">
            <search>
                <field name="builder_id"/>
                <field name="lang_id"/>
                <field name="source_property"/>
                <field name="source"/>
                <field name="value"/>
            </search>
        </field>
    </record>

    <record id="action_formio_builder_translation" model="ir.actions.act_window">
        <field name="name">Translations</field>
        <field name="res_model">formio.builder.translation</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="view_formio_builder_translation_tree"/>
        <field name="search_view_id" ref="view_formio_builder_translation_search"/>
    </record>
</odoo>
