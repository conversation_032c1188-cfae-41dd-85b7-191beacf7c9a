<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full copyright and licensing details. -->

<odoo>
    <!-- <data noupdate="1"> -->
    <data>
        <!-- formio.builder -->
        <record id="access_formio_builder_admin" model="ir.model.access">
            <field name="name">formio.builder: Admin</field>
            <field name="model_id" ref="formio.model_formio_builder"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_builder_user_all_forms" model="ir.model.access">
            <field name="name">formio.builder: User all forms</field>
            <field name="model_id" ref="formio.model_formio_builder"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_builder_user" model="ir.model.access">
            <field name="name">formio.builder: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_builder"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_builder_portal_user" model="ir.model.access">
            <field name="name">formio.builder: Portal User</field>
            <field name="model_id" ref="formio.model_formio_builder"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.form -->
        <record id="access_formio_form_admin" model="ir.model.access">
            <field name="name">formio.form: Admin</field>
            <field name="model_id" ref="formio.model_formio_form"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_form_user_all_forms" model="ir.model.access">
            <field name="name">formio.form: User all forms</field>
            <field name="model_id" ref="formio.model_formio_form"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_form_user" model="ir.model.access">
            <field name="name">formio.form: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_form"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_form_portal_user" model="ir.model.access">
            <field name="name">formio.form: Portal User</field>
            <field name="model_id" ref="formio.model_formio_form"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <!-- formio.version -->
        <record id="access_formio_version_admin" model="ir.model.access">
            <field name="name">formio.version: Admin</field>
            <field name="model_id" ref="formio.model_formio_version"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_version_user_all_forms" model="ir.model.access">
            <field name="name">formio.version: User all forms</field>
            <field name="model_id" ref="formio.model_formio_version"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_version_user" model="ir.model.access">
            <field name="name">formio.version: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_version"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_version_portal_user" model="ir.model.access">
            <field name="name">formio.version: Portal User</field>
            <field name="model_id" ref="formio.model_formio_version"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.version.asset -->
        <record id="access_formio_version_asset_admin" model="ir.model.access">
            <field name="name">formio.version.asset: Admin</field>
            <field name="model_id" ref="formio.model_formio_version_asset"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_version_asset_user_all_forms" model="ir.model.access">
            <field name="name">formio.version.asset: User all forms</field>
            <field name="model_id" ref="formio.model_formio_version_asset"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_version_asset_user" model="ir.model.access">
            <field name="name">formio.version.asset: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_version_asset"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_version_asset_portal_user" model="ir.model.access">
            <field name="name">formio.version.asset: Portal User</field>
            <field name="model_id" ref="formio.model_formio_version_asset"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.version.github.tag -->
        <record id="access_formio_version_github_tag_admin" model="ir.model.access">
            <field name="name">formio.version.github.tag: Admin</field>
            <field name="model_id" ref="formio.model_formio_version_github_tag"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_version_github_checker_wizard_admin" model="ir.model.access">
            <field name="name">formio.version.github.checker.wizard</field>
            <field name="model_id" ref="formio.model_formio_version_github_checker_wizard"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_version_github_tag_available_admin" model="ir.model.access">
            <field name="name">formio.version.github.tag.available</field>
            <field name="model_id" ref="formio.model_formio_version_github_tag_available"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <!-- formio.default.asset.css -->
        <record id="access_formio_default_asset_css_admin" model="ir.model.access">
            <field name="name">formio.default.asset.css: Admin</field>
            <field name="model_id" ref="formio.model_formio_default_asset_css"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <!-- formio.translation.source -->
        <record id="access_formio_translation_source_admin" model="ir.model.access">
            <field name="name">formio.translation.source: Admin</field>
            <field name="model_id" ref="formio.model_formio_translation_source"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_translation_source_user_all_forms" model="ir.model.access">
            <field name="name">formio.translation.source: User all forms</field>
            <field name="model_id" ref="formio.model_formio_translation_source"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_translation_source_user" model="ir.model.access">
            <field name="name">formio.translation.source: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_translation_source"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_translation_source_portal_user" model="ir.model.access">
            <field name="name">formio.translation.source: Portal User</field>
            <field name="model_id" ref="formio.model_formio_translation_source"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.translation -->
        <record id="access_formio_translation_admin" model="ir.model.access">
            <field name="name">formio.translation: Admin</field>
            <field name="model_id" ref="formio.model_formio_translation"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_translation_user_all_forms" model="ir.model.access">
            <field name="name">formio.translation: User all forms</field>
            <field name="model_id" ref="formio.model_formio_translation"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_translation_user" model="ir.model.access">
            <field name="name">formio.translation: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_translation"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_translation_portal_user" model="ir.model.access">
            <field name="name">formio.translation: Portal User</field>
            <field name="model_id" ref="formio.model_formio_translation"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.version.translation -->
        <record id="access_formio_version_translation_admin" model="ir.model.access">
            <field name="name">formio.version.translation: Admin</field>
            <field name="model_id" ref="formio.model_formio_version_translation"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_version_translation_user_all_forms" model="ir.model.access">
            <field name="name">formio.version.translation: User all forms</field>
            <field name="model_id" ref="formio.model_formio_version_translation"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_version_translation_user" model="ir.model.access">
            <field name="name">formio.version.translation: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_version_translation"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_version_translation_portal_user" model="ir.model.access">
            <field name="name">formio.version.translation: Portal User</field>
            <field name="model_id" ref="formio.model_formio_version_translation"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.res.model -->
        <record id="access_formio_res_model_admin" model="ir.model.access">
            <field name="name">formio.res.model: Admin</field>
            <field name="model_id" ref="formio.model_formio_res_model"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_res_model_user_all_forms" model="ir.model.access">
            <field name="name">formio.res.model: User all forms</field>
            <field name="model_id" ref="formio.model_formio_res_model"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_res_model_user" model="ir.model.access">
            <field name="name">formio.res.model: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_res_model"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_res_model_portal_user" model="ir.model.access">
            <field name="name">formio.res.model: Portal User</field>
            <field name="model_id" ref="formio.model_formio_res_model"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.builder.translation -->
        <record id="access_formio_builder_translation_admin" model="ir.model.access">
            <field name="name">formio.builder.translation: Admin</field>
            <field name="model_id" ref="formio.model_formio_builder_translation"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_builder_translation_user_all_forms" model="ir.model.access">
            <field name="name">formio.builder.translation: User all forms</field>
            <field name="model_id" ref="formio.model_formio_builder_translation"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_builder_translation_user" model="ir.model.access">
            <field name="name">formio.builder.translation: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_builder_translation"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_builder_translation_portal_user" model="ir.model.access">
            <field name="name">formio.builder.translation: Portal User</field>
            <field name="model_id" ref="formio.model_formio_builder_translation"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.builder.translation.model -->

        <record id="access_formio_builder_translation_model_admin" model="ir.model.access">
            <field name="name">formio.builder.translation.model: Admin</field>
            <field name="model_id" ref="formio.model_formio_builder_translation_model"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_builder_translation_model_user_all_forms" model="ir.model.access">
            <field name="name">formio.builder.translation.model: User all forms</field>
            <field name="model_id" ref="formio.model_formio_builder_translation_model"/>
            <field name="group_id" ref="formio.group_formio_user_all_forms"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_builder_translation_model_user" model="ir.model.access">
            <field name="name">formio.builder.translation.model: User assigned forms</field>
            <field name="model_id" ref="formio.model_formio_builder_translation_model"/>
            <field name="group_id" ref="formio.group_formio_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <record id="access_formio_builder_translation_model_portal_user" model="ir.model.access">
            <field name="name">formio.builder.translation.model: Portal User</field>
            <field name="model_id" ref="formio.model_formio_builder_translation_model"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- formio.builder.js.options -->
        <record id="access_formio_builder_js_options_admin" model="ir.model.access">
            <field name="name">formio.builder.js.options: Admin</field>
            <field name="model_id" ref="formio.model_formio_builder_js_options"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <record id="access_formio_builder_js_options_merge_admin" model="ir.model.access">
            <field name="name">formio.builder.js.options.merge: Admin</field>
            <field name="model_id" ref="formio.model_formio_builder_js_options_merge"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>

        <!-- formio.license -->
        <record id="access_formio_license_admin" model="ir.model.access">
            <field name="name">formio.license: Admin</field>
            <field name="model_id" ref="formio.model_formio_license"/>
            <field name="group_id" ref="formio.group_formio_admin"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>
    </data>
</odoo>
