# Copyright 2016 ICTSTUDIO <http://www.ictstudio.eu>
# Copyright 2021 ACSONE SA/NV <https://acsone.eu>
# License: AGPL-3.0 or later (http://www.gnu.org/licenses/agpl)

{
    "name": "Authentication OpenID Connect",
    "version": "18.0.1.0.0",
    "license": "AGPL-3",
    "author": (
        "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, "
        "ACSONE SA/NV, "
        "Odoo Community Association (OCA)"
    ),
    "maintainers": ["sbidoul"],
    "website": "https://github.com/OCA/server-auth",
    "summary": "Allow users to login through OpenID Connect Provider",
    "external_dependencies": {"python": ["python-jose"]},
    "depends": ["auth_oauth"],
    "data": ["views/auth_oauth_provider.xml", "data/auth_oauth_data.xml"],
    "demo": ["demo/local_keycloak.xml"],
}
