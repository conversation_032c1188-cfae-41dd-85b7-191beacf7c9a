from odoo import http
from odoo.http import request
from werkzeug.utils import redirect as werkzeug_redirect

import urllib.parse
import logging
_logger = logging.getLogger(__name__)

class LogoutController(http.Controller):

    @http.route('/web/session/logout', type='http', auth="user")
    def custom_logout(self, redirect=None, **kwargs):
        # Recherche du provider Keycloak configuré (adapter si nécessaire)
        provider = request.env['auth.oauth.provider'].search([('name', '=', 'Keycloak')], limit=1)
        _logger.info('Provider: %s' % provider)
        if provider and provider.logout_endpoint:
            _logger.info('Logout endpoint: %s' % provider.logout_endpoint)
            # session
            _logger.info('Session: %s' % request.session)
            id_token = request.session.get('oidc_id_token')
            _logger.info('id_token: %s' % id_token)
            if id_token:
                # Configuration de l'URL de redirection après logout
                # Utiliser l'URL de base d'Odoo
                post_logout_redirect = request.httprequest.url_root.rstrip('/')
                params = {
                    'id_token_hint': id_token,
                    'post_logout_redirect_uri': post_logout_redirect,
                }
                logout_url = f"{provider.logout_endpoint}?{urllib.parse.urlencode(params)}"
                _logger.info('Logout URL: %s' % logout_url)
                # Détruire la session Odoo avant redirection
                request.session.logout(keep_db=True)
                return werkzeug_redirect(logout_url)

        # Si pas de config logout, logout Odoo classique
        request.session.logout(keep_db=True)
        return request.redirect('/')
