<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full copyright and licensing details. -->

<odoo>
    <data noupdate="1">
        <record id="formio_form_user_all" model="ir.rule">
            <field name="name">formio.form: User all forms</field>
            <field name="model_id" ref="model_formio_form"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('formio.group_formio_user_all_forms'))]"/>
            <field name="domain_force">[]</field>
        </record>

        <record id="formio_form_user_assigned" model="ir.rule">
            <field name="name">formio.form: User assigned forms</field>
            <field name="model_id" ref="model_formio_form"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
            <field name="groups" eval="[(4, ref('formio.group_formio_user'))]"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
        </record>

        <record id="formio_form_unlink_user_assigned" model="ir.rule">
            <field name="name">formio.form unlink: User assigned forms</field>
            <field name="model_id" ref="model_formio_form"/>
            <field name="perm_read" eval="False"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('formio.group_formio_user'))]"/>
            <field name="domain_force">
                [('create_uid', '=', user.id), ('user_id', '=', user.id), ('state', '!=', 'COMPLETE')]
            </field>
        </record>
        
        <record id="formio_form_portal_user" model="ir.rule">
            <field name="name">formio.form read, write: Portal User</field>
            <field name="model_id" ref="model_formio_form"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="domain_force">[('builder_id.portal', '=', True), ('user_id', '=', user.id)]</field>
        </record>

        <record id="formio_form_unlink_portal_user" model="ir.rule">
            <field name="name">formio.form unlink: Portal User</field>
            <field name="model_id" ref="model_formio_form"/>
            <field name="perm_read" eval="False"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="domain_force">
                [
                '&amp;',
                '&amp;',
                '&amp;',
                ('builder_id.portal', '=', True), ('create_uid', '=', user.id), ('user_id', '=', user.id), ('state', '!=', 'COMPLETE')]
            </field>
        </record>
    </data>
</odoo>
