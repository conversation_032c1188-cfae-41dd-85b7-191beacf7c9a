# FedaPay

## Technical details

API: [FedaPay Standard Checkout](https://docs.fedapay.com/introduction/fr/checkoutjs-fr)

SDK: [JavaScript SDK](https://docs.fedapay.com/introduction/fr/checkoutjs-fr)

This module relies on the "Javascript" SDK to render the FedaPay payment button in place of the
generic payment form's submit button. The assets of the SDK are loaded dynamically when a payment
method is selected.

When the FedaPay button is clicked, a server-to-server API call is made to create the order on FedaPay
side and a FedaPay modal is opened. When the order is confirmed within the modal, another call is
made to finalize the payment.

## Supported features

- Direct payment flow
- Webhook notifications

## Module history

- `18.0`
  - The NVP/SOAP API that allowed for redirect payments is replaced by a combination of the
    JavaScript SDK and the Standard Checkout API. odoo/odoo#167402
- `17.0`
  - The support for customer fees is removed as it is no longer supported by the `payment` module.
    odoo/odoo#132104
- `16.2`
  - The "Merchant Account ID" and "Use IPN" fields are removed. odoo/odoo#104974
- `16.1`
  - Customer fees are converted into the currency of the payment transaction. odoo/odoo#100156
- `15.2`
  - An HTTP 404 "Forbidden" error is raised instead of a Validation error when the authenticity of
    the webhook notification cannot be verified. odoo/odoo#81607

## Testing instructions

Payments must be made using a separate [sandbox account](https://sandbox.fedapay.com/register).

Read more at https://developer.paypal.com/tools/sandbox/.
