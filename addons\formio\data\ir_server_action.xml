<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="action_server_formio_version_reset_download_install" model="ir.actions.server">
        <field name="name">Reset (download and reinstall)</field>
        <field name="model_id" ref="formio.model_formio_version"/>
        <field name="binding_model_id" ref="formio.model_formio_version"/>
        <field name="state">code</field>
        <field name="code">
records.action_reset_download_install()
        </field>
    </record>

    <record id="action_formio_builder_reset_formio_version" model="ir.actions.server">
        <field name="name">Reset formio.js version to Dummy</field>
        <field name="model_id" ref="formio.model_formio_builder"/>
        <field name="binding_model_id" ref="formio.model_formio_builder"/>
        <field name="state">code</field>
        <field name="code">
records.write({'formio_version_id': env.ref('formio.version_dummy').id})
        </field>
    </record>

</odoo>
