<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <template id="formio_builder_embed" name="formio builder - embed">
        &lt;!DOCTYPE html&gt;
        <html>
            <head>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>

                <t t-foreach="formio_css_assets" t-as="css">
                    <link rel="stylesheet" type="text/css" t-att-href="css.url"/>
                </t>
                <t t-foreach="formio_js_assets" t-as="js">
                    <script type="text/javascript" t-att-src="js.url"></script>
                </t>

                <link rel="stylesheet" t-attf-href="/formio/static/src/css/formio_builder_embed.css?{{ uuid }}"/>

                <!-- Odoo JS dependencies could cause clashes with formio.js -->
                <script type="text/javascript" src="/web/static/lib/jquery/jquery.js"></script>
                <script type="text/javascript" src="/web/static/lib/owl/owl.js"></script>

                <script type="text/javascript" t-attf-src="/formio/static/src/js/builder/app.js?{{ uuid }}"/>
            </head>
            <body>
                <!-- API alert -->
                <div t-if="builder.show_api_alert" class="alert alert-warning p-2 ml-1 mr-1">
                    <strong>WARNING: </strong><span>Be careful when updating or removing components, as there are integrated APIs <i class="fa fa-arrow-right"/> <t t-out="builder.api_alert"/></span>
                </div>
                <!-- Languages -->
                <t t-if="len(builder_languages) > 1">
                    <div class="formio_languages">
                        <t t-foreach="builder_languages" t-as="lang">
                            <button class="btn btn-sm btn-default" t-att-lang="lang.formio_ietf_code" t-attf-onclick="setLanguage('{{ lang.formio_ietf_code }}', this)">
                                <span t-field="lang.name"/>
                            </button>
                        </t>
                    </div>
                </t>
                <!-- Actions top -->
                <div id="formio_builder_actions_top" class="formio_builder_actions row">
                    <div t-if="not builder.is_locked" class="col-xs-6 col-sm-3 col-md-2">
                        <t t-if="not builder.auto_save">
                            <a href="#" class="btn btn-warning d-none formio_save" t-attf-onclick="saveFormBuilder(this)"><i class="fa fa-xl fa-save"/> Save Form</a>
                        </t>
                        <t t-else="">
                            <div class="text-muted formio_text">Auto-save Enabled</div>
                        </t>
                    </div>
                </div>
                <!-- Form builder shall be mounted here -->
                <div id="formio_builder_app">
                    <div id="formio_builder_loading"></div>
                </div>
                <!-- Actions bottom -->
                <div id="formio_builder_actions_bottom" class="formio_builder_actions row">
                    <div t-if="not builder.is_locked" class="col-xs-6 col-sm-3 col-md-2">
                        <t t-if="not builder.auto_save">
                            <a href="#" class="btn btn-warning d-none formio_save" t-attf-onclick="saveFormBuilder(this)"><i class="fa fa-xl fa-save"/> Save Form</a>
                        </t>
                        <t t-else="">
                            <div class="text-muted formio_text">Auto-save Enabled</div>
                        </t>
                    </div>
                </div>
                <input type="hidden" id="builder_id" name="builder_id" t-att-value="builder.id"/>
            </body>
        </html>
    </template>
</odoo>
