<section class="oe_container">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;"><b>Form Builder & Forms integration</b></h2>
    <h3 class="oe_slogan mb-0 mt8">Build easy-to-use, professional and versatile Forms (backend, portal, website, embedded) to integrate and collect any information you need for your business.</h3>
  </div>
  <div class="oe_centered">
    <h4 class="oe_mb16 text-center">Compatible with Enterprise and Community</h4>
    <h4 class="oe_mb16 text-center">Works on Odoo.sh</h4>
  </div>
</section>

<section class="oe_container mt-4">
  <div class="row">
    <div class="offset-lg-0 offset-md-1 col-lg-6 col-md-11">
      <img class="oe_demo oe_picture oe_screenshot" src="intro-screenshot-1.png">
    </div>
    <div class="offset-lg-0 offset-md-1 col-lg-6 col-md-11">
      <img class="oe_demo oe_picture oe_screenshot" src="intro-screenshot-2.png">
    </div>
  </div>
</section>

<section class="oe_container mt-4">
  <h2 class="oe_slogan" style="color:#875A7B;">Features</h2>
  <table class="table table-bordered">
    <colgroup>
       <col span="1" style="width: 35%;">
       <col span="1" style="width: 65%;">
    </colgroup>
    <thead>
      <tr style="background-color: #875A7B; color: #fff;">
        <th>Subject</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><strong>Form Builder</strong></td>
        <td>
          - Powerful "drag & drop" Form Builder, with lots of features.<br/>
          - Form Builders (designs) are stored in database (form builder) records.
        </td>
      </tr>
      <tr>
        <td><strong>Forms</strong></td>
        <td>
          - Form submissions are stored in database (form) records.<br/>
          - Create / publish / share a Form internally, website, portal, by email or a hyperlink.<br/>
          - Assign Form(s) to (portal) user and send invitation by mail.<br/>
          - View, analyse, use and share Forms (submissions data).<br/>
        </td>
      </tr>
      <tr>
        <td><strong>JSON data</strong></td>
        <td>
          - A Form Builder (design) is stored as JSON data in the schema field of a form builder record.<br/>
          - A submitted Form (input data) is stored as JSON data in the submission field of a form record.
        </td>
      </tr>
      <tr>
      <tr>
        <td><strong>Highly configurable</strong></td>
        <td>Easily configure your Forms deployment, publishing and other functionalities (in the Form Builder).</td>
      </tr>
      <tr>
        <td><strong>Integrations</strong></td>
        <td>
          The integration possibilities are unlimited:<br/>
          - Integrate Forms with other Odoo apps (modules), for example:<br/>
          CRM, Helpdesk, ERP, Sales, HR, shop, e-commerce, website<br/>
          - Process Form submissions to integrate the form data with any requirement.<br/>
          - Visit the other Forms modules to get a basic idea of configurable integration modules -- Click the author link <strong style="color: #875A7B;">"Nova Code"</strong> in the top.
        </td>
      </tr>
      <tr>
        <td><strong>APIs (in Form Builder) to populate Form fields</td>
        <td>
          Populate Form fields with data from other Odoo apps or by custom solutions and APIs.<br/><br/>
          Examples:<br/>
          - For a logged-in portal user, populate form fields with company (parent) details, address, invoices, shipments.<br/>
          - Populate a select/dropdown field where choices are products, filtered by some product-category or product attributes.<br/><br/>
          This requires the Components API module: <code>https://apps.odoo.com/apps/modules/18.0/formio_components_api</code>
        </td>
      </tr>
      <tr>
        <td><strong>Multilingual &amp; Translations management</strong></td>
        <td>
          - Forms and Builders are multilingual.<br/>
          - Language-switch buttons are automatically available in the form and form builder.<br/>
          - Manage custom translations (e.g. labels, select choices, buttons) in the Form Builder.<br/>
          - The default language is determined and automatically determined / set by: website &rarr; logged in (portal) user.<br/>
          - If the website is enabled, the language (translations) also react upon the website language-switch.
        </td>
      </tr>
      <tr>
        <td><strong>Embed Forms on any Website</strong></td>
        <td>
          Embedding a Form on any website is easy.<br/>
          A redirect after submission can also be set up.
        </td>
      </tr>
      <tr>
        <td><strong>Upgrade-tool formio.js library assets</strong></td>
        <td>Upgrade the formio.js library assets (js, css) when a new version is available, by just a few clicks.</td>
      </tr>
      <tr>
        <td><strong>Developer Friendly</strong></td>
        <td>
          - Extend, customize the Forms modules.<br/>
          - Develop new Forms modules.<br/>
          - Develop custom Form components e.g. QR-code scanning, a value slider, reCAPTCHA component (already released as module) etc.
        </td>
      </tr>
      <tr>
        <td><strong>Installation</strong></td>
        <td>After installation you can start right away. No extra technical requirements or setup.</td>
      </tr>
      <tr>
        <td><strong>Odoo database migration</strong></td>
        <td>
          Form builders, forms (submissions) and other related data remain compatible in newer Odoo versions, even when you migrate the database.<br/>
          Mainly due to the JSON data storage of the form builders (designs) and form submissions.
        </td>
      </tr>
      <tr>
        <td><strong>We <i class="fa fa-heart"/> open source</strong></td>
        <td>
          LGPL-3 license
        </td>
      </tr>
    </tbody>
  </table>
</section>

<section class="oe_container oe_dark mt-4">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;">Form Builders</h2>
    <h3 class="oe_slogan">Configure and design</h3>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Form Builders</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="builder-list.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Form Builder Configuration</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="builder-edit.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Form Builder</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="builder-formio.png">
    </div>
  </div>
</section>

<section class="oe_container">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;">Forms & Submissions</h2>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Create or share a Form: internal, website, portal, by email or a hyperlink</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="form-odoo-create.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Form: Pending</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="form-formio.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Form: Completed</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="form-formio-completed.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Form: Submission form-view</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="form-details.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Form Submissions</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="form-list.png">
    </div>
  </div>
</section>

<section class="oe_container oe_dark">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;">Module configuration</h2>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Settings</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="formio-config-settings.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">List of Available &amp; Installed formio.js versions </h4>
      <img class="oe_demo oe_picture oe_screenshot" src="formio-version-check-install.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Download &amp; Install Available formio.js versions</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="formio-version-download-install.png">
    </div>
  </div>
</section>

<section class="oe_container mt-4">
  <div class="oe_row oe_centeralign mb32">
    <h2 class="oe_slogan" style="color:#875A7B;">Questions? Support and training</h2>
    <h5>
      <i class="fa fa-life-ring"/> Contact Us<br/><br/>
      <code>https://www.novaforms.app/contact</code>
    </h5>
  </div>
  <div class="oe_row oe_centeralign mb32">
    <h2 class="oe_slogan" style="color:#875A7B;">Visit the online Demo</h2>
    <h5><i class="fa fa-desktop"/> Click the <em>"Live Preview"</em> button in the top.</h5>
    <h6 class="mt32">User Login:</h6>
    <p class="mt8">User: demo</p>
    <p class="mt8">Password: demo</p>
  </div>
  <div class="oe_row oe_centeralign mb16">
    <h2 class="oe_slogan" style="color:#875A7B;">Releases and Changelog</h2>
    <h5>
      <i class="fa fa-github"/> Changelog is available on the GitHub project<br/><br/>
      <code>https://github.com/novacode-nl/odoo-formio/blob/18.0/formio/CHANGELOG.md</code>
    </h5>
    <h5 class="mt32">
      <i class="fa fa-github"/> Star or subscribe the GitHub project for notifications<br/><br/>
      <code>https://github.com/novacode-nl/odoo-formio</code>
    </h5>
  </div>
</section>

<section class="oe_container">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;">Higly recommended additional modules</h2>
    <h3 class="oe_slogan">
      <small>The modules below get your forms usage and implementation to the next level.</small><br/>
    </h3>
      <table class="table table-bordered">
      <colgroup>
        <col span="1" style="width: 20%;">
        <col span="1" style="width: 70%;">
        <col span="1" style="width: 20%;">
      </colgroup>
      <thead>
        <tr style="background-color: #875A7B; color: #fff;">
          <th>Name</th>
          <th>URL</th>
          <th>Pricing</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Website Snippet</td>
          <td><code>https://apps.odoo.com/apps/modules/18.0/website_formio_snippet/</code></td>
          <td>Premium, purchase a license</td>
        </tr>
        <tr>
        <tr>
          <td>Filestore Storage</td>
          <td><code>https://apps.odoo.com/apps/modules/18.0/formio_storage_filestore/</code></td>
          <td>Free</td>
        </tr>
        <tr>
          <td>QWeb Reports</td>
          <td><code>https://apps.odoo.com/apps/modules/18.0/formio_report_qweb/</code></td>
          <td>Free</td>
        </tr>
        <tr>
          <td>Components API</td>
          <td><code>https://apps.odoo.com/apps/modules/18.0/formio_components_api/</code></td>
          <td>Premium, purchase a license</td>
        </tr>
        <tr>
          <td>Theming</td>
          <td><code>https://apps.odoo.com/apps/modules/18.0/formio_theming/</code></td>
          <td>Premium, purchase a license</td>
        </tr>
        <tr>
          <td>ETL Interface</td>
          <td><code>https://apps.odoo.com/apps/modules/18.0/formio_etl/</code></td>
          <td>Premium, purchase a license</td>
        </tr>
        <tr>
          <td>Deploy Tool</td>
          <td><code>https://apps.odoo.com/apps/modules/18.0/formio_deploy/</code></td>
          <td>Premium, purchase a license</td>
          <td>
        </tr>
      </tbody>
    </table>
  </div>
</section>
