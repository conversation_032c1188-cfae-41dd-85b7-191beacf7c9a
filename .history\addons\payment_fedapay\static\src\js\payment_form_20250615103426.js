/* global FedaPay */
import { loadJS } from '@web/core/assets';
import paymentForm from '@payment/js/payment_form';

paymentForm.include({
    async _prepareInlineForm(providerId, providerCode, paymentOptionId, paymentMethodCode, flow) {
        if (providerCode !== 'fedapay') {
            this._super(...arguments);
            return;
        }

        const radio = document.querySelector('input[name="o_payment_radio"]:checked');
        const inlineFormValues = JSON.parse(radio.dataset.fedapayInline_form_values);

        // Load Fedapay SDK
        await loadJS('https://cdn.fedapay.com/checkout.js?v=1.1.7');

        const btn = document.getElementById('pay-btn');
        FedaPay.init(btn, {
            public_key: inlineFormValues.public_key,
            onSuccess: (response) => {
                console.log('Success:', response);
                window.location.href = '/payment/status';
            },
            onError: (error) => {
                console.error('Error:', error);
                alert('Payment failed!');
            }
        });
    }
});