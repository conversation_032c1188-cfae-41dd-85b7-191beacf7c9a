<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <record id="mail_activity_type_action_config_formio" model="ir.actions.act_window">
        <field name="name">Activity Types</field>
        <field name="res_model">mail.activity.type</field>
        <field name="view_mode">list,form</field>
        <field name="domain">['|', ('res_model', '=', False), ('res_model', '=', 'formio.form')]</field>
        <field name="context">{'default_res_model': 'formio.form'}</field>
    </record>
    <menuitem id="formio_menu_config_activity_type"
        action="mail_activity_type_action_config_formio"
        parent="menu_formio_configuration"
        sequence="900"
        groups="base.group_no_one"/>
</odoo>
