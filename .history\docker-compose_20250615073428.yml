version: '3.7'

services:
  web:
    image: odoo:18
    depends_on:
      - db
    ports:
      - "10018:8069"
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./modules:/mnt/extra-addons
      - ./etc:/etc/odoo
    environment:
      HOST: db
      USER: odoo
      PASSWORD: odoo

  db:
    image: postgres:17
    environment:
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      POSTGRES_DB: postgres
    volumes:
      - odoo-db-data:/var/lib/postgresql/data

volumes:
  odoo-web-data:
  odoo-db-data:
