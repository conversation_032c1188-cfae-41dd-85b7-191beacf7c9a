<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils: https://docutils.sourceforge.io/" />
<title>Authentication OpenID Connect</title>
<style type="text/css">

/*
:Author: <PERSON> (<EMAIL>)
:Id: $Id: html4css1.css 9511 2024-01-13 09:50:07Z milde $
:Copyright: This stylesheet has been placed in the public domain.

Default cascading style sheet for the HTML output of Docutils.
Despite the name, some widely supported CSS2 features are used.

See https://docutils.sourceforge.io/docs/howto/html-stylesheets.html for how to
customize this style sheet.
*/

/* used to remove borders from tables and images */
.borderless, table.borderless td, table.borderless th {
  border: 0 }

table.borderless td, table.borderless th {
  /* Override padding for "table.docutils td" with "! important".
     The right padding separates the table cells. */
  padding: 0 0.5em 0 0 ! important }

.first {
  /* Override more specific margin styles with "! important". */
  margin-top: 0 ! important }

.last, .with-subtitle {
  margin-bottom: 0 ! important }

.hidden {
  display: none }

.subscript {
  vertical-align: sub;
  font-size: smaller }

.superscript {
  vertical-align: super;
  font-size: smaller }

a.toc-backref {
  text-decoration: none ;
  color: black }

blockquote.epigraph {
  margin: 2em 5em ; }

dl.docutils dd {
  margin-bottom: 0.5em }

object[type="image/svg+xml"], object[type="application/x-shockwave-flash"] {
  overflow: hidden;
}

/* Uncomment (and remove this text!) to get bold-faced definition list terms
dl.docutils dt {
  font-weight: bold }
*/

div.abstract {
  margin: 2em 5em }

div.abstract p.topic-title {
  font-weight: bold ;
  text-align: center }

div.admonition, div.attention, div.caution, div.danger, div.error,
div.hint, div.important, div.note, div.tip, div.warning {
  margin: 2em ;
  border: medium outset ;
  padding: 1em }

div.admonition p.admonition-title, div.hint p.admonition-title,
div.important p.admonition-title, div.note p.admonition-title,
div.tip p.admonition-title {
  font-weight: bold ;
  font-family: sans-serif }

div.attention p.admonition-title, div.caution p.admonition-title,
div.danger p.admonition-title, div.error p.admonition-title,
div.warning p.admonition-title, .code .error {
  color: red ;
  font-weight: bold ;
  font-family: sans-serif }

/* Uncomment (and remove this text!) to get reduced vertical space in
   compound paragraphs.
div.compound .compound-first, div.compound .compound-middle {
  margin-bottom: 0.5em }

div.compound .compound-last, div.compound .compound-middle {
  margin-top: 0.5em }
*/

div.dedication {
  margin: 2em 5em ;
  text-align: center ;
  font-style: italic }

div.dedication p.topic-title {
  font-weight: bold ;
  font-style: normal }

div.figure {
  margin-left: 2em ;
  margin-right: 2em }

div.footer, div.header {
  clear: both;
  font-size: smaller }

div.line-block {
  display: block ;
  margin-top: 1em ;
  margin-bottom: 1em }

div.line-block div.line-block {
  margin-top: 0 ;
  margin-bottom: 0 ;
  margin-left: 1.5em }

div.sidebar {
  margin: 0 0 0.5em 1em ;
  border: medium outset ;
  padding: 1em ;
  background-color: #ffffee ;
  width: 40% ;
  float: right ;
  clear: right }

div.sidebar p.rubric {
  font-family: sans-serif ;
  font-size: medium }

div.system-messages {
  margin: 5em }

div.system-messages h1 {
  color: red }

div.system-message {
  border: medium outset ;
  padding: 1em }

div.system-message p.system-message-title {
  color: red ;
  font-weight: bold }

div.topic {
  margin: 2em }

h1.section-subtitle, h2.section-subtitle, h3.section-subtitle,
h4.section-subtitle, h5.section-subtitle, h6.section-subtitle {
  margin-top: 0.4em }

h1.title {
  text-align: center }

h2.subtitle {
  text-align: center }

hr.docutils {
  width: 75% }

img.align-left, .figure.align-left, object.align-left, table.align-left {
  clear: left ;
  float: left ;
  margin-right: 1em }

img.align-right, .figure.align-right, object.align-right, table.align-right {
  clear: right ;
  float: right ;
  margin-left: 1em }

img.align-center, .figure.align-center, object.align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

table.align-center {
  margin-left: auto;
  margin-right: auto;
}

.align-left {
  text-align: left }

.align-center {
  clear: both ;
  text-align: center }

.align-right {
  text-align: right }

/* reset inner alignment in figures */
div.align-right {
  text-align: inherit }

/* div.align-center * { */
/*   text-align: left } */

.align-top    {
  vertical-align: top }

.align-middle {
  vertical-align: middle }

.align-bottom {
  vertical-align: bottom }

ol.simple, ul.simple {
  margin-bottom: 1em }

ol.arabic {
  list-style: decimal }

ol.loweralpha {
  list-style: lower-alpha }

ol.upperalpha {
  list-style: upper-alpha }

ol.lowerroman {
  list-style: lower-roman }

ol.upperroman {
  list-style: upper-roman }

p.attribution {
  text-align: right ;
  margin-left: 50% }

p.caption {
  font-style: italic }

p.credits {
  font-style: italic ;
  font-size: smaller }

p.label {
  white-space: nowrap }

p.rubric {
  font-weight: bold ;
  font-size: larger ;
  color: maroon ;
  text-align: center }

p.sidebar-title {
  font-family: sans-serif ;
  font-weight: bold ;
  font-size: larger }

p.sidebar-subtitle {
  font-family: sans-serif ;
  font-weight: bold }

p.topic-title {
  font-weight: bold }

pre.address {
  margin-bottom: 0 ;
  margin-top: 0 ;
  font: inherit }

pre.literal-block, pre.doctest-block, pre.math, pre.code {
  margin-left: 2em ;
  margin-right: 2em }

pre.code .ln { color: gray; } /* line numbers */
pre.code, code { background-color: #eeeeee }
pre.code .comment, code .comment { color: #5C6576 }
pre.code .keyword, code .keyword { color: #3B0D06; font-weight: bold }
pre.code .literal.string, code .literal.string { color: #0C5404 }
pre.code .name.builtin, code .name.builtin { color: #352B84 }
pre.code .deleted, code .deleted { background-color: #DEB0A1}
pre.code .inserted, code .inserted { background-color: #A3D289}

span.classifier {
  font-family: sans-serif ;
  font-style: oblique }

span.classifier-delimiter {
  font-family: sans-serif ;
  font-weight: bold }

span.interpreted {
  font-family: sans-serif }

span.option {
  white-space: nowrap }

span.pre {
  white-space: pre }

span.problematic, pre.problematic {
  color: red }

span.section-subtitle {
  /* font-size relative to parent (h1..h6 element) */
  font-size: 80% }

table.citation {
  border-left: solid 1px gray;
  margin-left: 1px }

table.docinfo {
  margin: 2em 4em }

table.docutils {
  margin-top: 0.5em ;
  margin-bottom: 0.5em }

table.footnote {
  border-left: solid 1px black;
  margin-left: 1px }

table.docutils td, table.docutils th,
table.docinfo td, table.docinfo th {
  padding-left: 0.5em ;
  padding-right: 0.5em ;
  vertical-align: top }

table.docutils th.field-name, table.docinfo th.docinfo-name {
  font-weight: bold ;
  text-align: left ;
  white-space: nowrap ;
  padding-left: 0 }

/* "booktabs" style (no vertical lines) */
table.docutils.booktabs {
  border: 0px;
  border-top: 2px solid;
  border-bottom: 2px solid;
  border-collapse: collapse;
}
table.docutils.booktabs * {
  border: 0px;
}
table.docutils.booktabs th {
  border-bottom: thin solid;
  text-align: left;
}

h1 tt.docutils, h2 tt.docutils, h3 tt.docutils,
h4 tt.docutils, h5 tt.docutils, h6 tt.docutils {
  font-size: 100% }

ul.auto-toc {
  list-style-type: none }

</style>
</head>
<body>
<div class="document" id="authentication-openid-connect">
<h1 class="title">Authentication OpenID Connect</h1>

<!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!! This file is generated by oca-gen-addon-readme !!
!! changes will be overwritten.                   !!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!! source digest: sha256:cd754fc72d2039d02ab1b8aec98af43fb9543c9a70f2150ab6e482954e4e83d6
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
<p><a class="reference external image-reference" href="https://odoo-community.org/page/development-status"><img alt="Beta" src="https://img.shields.io/badge/maturity-Beta-yellow.png" /></a> <a class="reference external image-reference" href="http://www.gnu.org/licenses/agpl-3.0-standalone.html"><img alt="License: AGPL-3" src="https://img.shields.io/badge/licence-AGPL--3-blue.png" /></a> <a class="reference external image-reference" href="https://github.com/OCA/server-auth/tree/18.0/auth_oidc"><img alt="OCA/server-auth" src="https://img.shields.io/badge/github-OCA%2Fserver--auth-lightgray.png?logo=github" /></a> <a class="reference external image-reference" href="https://translation.odoo-community.org/projects/server-auth-18-0/server-auth-18-0-auth_oidc"><img alt="Translate me on Weblate" src="https://img.shields.io/badge/weblate-Translate%20me-F47D42.png" /></a> <a class="reference external image-reference" href="https://runboat.odoo-community.org/builds?repo=OCA/server-auth&amp;target_branch=18.0"><img alt="Try me on Runboat" src="https://img.shields.io/badge/runboat-Try%20me-875A7B.png" /></a></p>
<p>This module allows users to login through an OpenID Connect provider
using the authorization code flow or implicit flow.</p>
<p>Note the implicit flow is not recommended because it exposes access
tokens to the browser and in http logs.</p>
<p><strong>Table of contents</strong></p>
<div class="contents local topic" id="contents">
<ul class="simple">
<li><a class="reference internal" href="#installation" id="toc-entry-1">Installation</a></li>
<li><a class="reference internal" href="#configuration" id="toc-entry-2">Configuration</a><ul>
<li><a class="reference internal" href="#setup-for-microsoft-azure" id="toc-entry-3">Setup for Microsoft Azure</a></li>
<li><a class="reference internal" href="#setup-for-keycloak" id="toc-entry-4">Setup for Keycloak</a></li>
</ul>
</li>
<li><a class="reference internal" href="#usage" id="toc-entry-5">Usage</a></li>
<li><a class="reference internal" href="#known-issues-roadmap" id="toc-entry-6">Known issues / Roadmap</a></li>
<li><a class="reference internal" href="#changelog" id="toc-entry-7">Changelog</a><ul>
<li><a class="reference internal" href="#section-1" id="toc-entry-8">18.0.1.0.0 2024-10-09</a></li>
<li><a class="reference internal" href="#section-2" id="toc-entry-9">17.0.1.0.0 2024-03-20</a></li>
<li><a class="reference internal" href="#section-3" id="toc-entry-10">16.0.1.1.0 2024-02-28</a></li>
<li><a class="reference internal" href="#section-4" id="toc-entry-11">16.0.1.0.2 2023-11-16</a></li>
<li><a class="reference internal" href="#section-5" id="toc-entry-12">16.0.1.0.1 2023-10-09</a></li>
<li><a class="reference internal" href="#section-6" id="toc-entry-13">16.0.1.0.0 2023-01-27</a></li>
<li><a class="reference internal" href="#section-7" id="toc-entry-14">15.0.1.0.0 2023-01-06</a></li>
<li><a class="reference internal" href="#section-8" id="toc-entry-15">14.0.1.0.0 2021-12-10</a></li>
<li><a class="reference internal" href="#section-9" id="toc-entry-16">13.0.1.0.0 2020-04-10</a></li>
<li><a class="reference internal" href="#section-10" id="toc-entry-17">10.0.1.0.0 2018-10-05</a></li>
</ul>
</li>
<li><a class="reference internal" href="#bug-tracker" id="toc-entry-18">Bug Tracker</a></li>
<li><a class="reference internal" href="#credits" id="toc-entry-19">Credits</a><ul>
<li><a class="reference internal" href="#authors" id="toc-entry-20">Authors</a></li>
<li><a class="reference internal" href="#contributors" id="toc-entry-21">Contributors</a></li>
<li><a class="reference internal" href="#maintainers" id="toc-entry-22">Maintainers</a></li>
</ul>
</li>
</ul>
</div>
<div class="section" id="installation">
<h1><a class="toc-backref" href="#toc-entry-1">Installation</a></h1>
<p>This module depends on the
<a class="reference external" href="https://pypi.org/project/python-jose/">python-jose</a> library, not to
be confused with <tt class="docutils literal">jose</tt> which is also available on PyPI.</p>
</div>
<div class="section" id="configuration">
<h1><a class="toc-backref" href="#toc-entry-2">Configuration</a></h1>
<div class="section" id="setup-for-microsoft-azure">
<h2><a class="toc-backref" href="#toc-entry-3">Setup for Microsoft Azure</a></h2>
<p>Example configuration with OpenID Connect authorization code flow.</p>
<ol class="arabic simple">
<li>configure a new web application in Azure with OpenID and code flow
(see the <a class="reference external" href="https://docs.microsoft.com/en-us/powerapps/maker/portals/configure/configure-openid-provider">provider
documentation</a>))</li>
<li>in this application the redirect url must be be “&lt;url of your
server&gt;/auth_oauth/signin” and of course this URL should be reachable
from Azure</li>
<li>create a new authentication provider in Odoo with the following
parameters (see the <a class="reference external" href="https://docs.microsoft.com/en-us/powerapps/maker/portals/configure/configure-openid-settings">portal
documentation</a>
for more information):</li>
</ol>
<p><img alt="image" src="https://raw.githubusercontent.com/OCA/server-auth/18.0/auth_oidc/static/description/oauth-microsoft_azure-api_permissions.png" /></p>
<p><img alt="image1" src="https://raw.githubusercontent.com/OCA/server-auth/18.0/auth_oidc/static/description/oauth-microsoft_azure-optional_claims.png" /></p>
<p>Single tenant provider limits the access to user of your tenant, while
Multitenants allow access for all AzureAD users, so user of foreign
companies can use their AzureAD login without an guest account.</p>
<ul class="simple">
<li>Provider Name: Azure AD Single Tenant</li>
<li>Client ID: Application (client) id</li>
<li>Client Secret: Client secret</li>
<li>Allowed: yes</li>
</ul>
<p>or</p>
<ul class="simple">
<li>Provider Name: Azure AD Multitenant</li>
<li>Client ID: Application (client) id</li>
<li>Client Secret: Client secret</li>
<li>Allowed: yes</li>
<li>replace {tenant_id} in urls with your Azure tenant id</li>
</ul>
<p><img alt="image2" src="https://raw.githubusercontent.com/OCA/server-auth/18.0/auth_oidc/static/description/odoo-azure_ad_multitenant.png" /></p>
</div>
<div class="section" id="setup-for-keycloak">
<h2><a class="toc-backref" href="#toc-entry-4">Setup for Keycloak</a></h2>
<p>Example configuration with OpenID Connect authorization code flow.</p>
<p>In Keycloak:</p>
<ol class="arabic simple">
<li>configure a new Client</li>
<li>make sure Authorization Code Flow is Enabled.</li>
<li>configure the client Access Type as “confidential” and take note of
the client secret in the Credentials tab</li>
<li>configure the redirect url to be “&lt;url of your
server&gt;/auth_oauth/signin”</li>
</ol>
<p>In Odoo, create a new Oauth Provider with the following parameters:</p>
<ul class="simple">
<li>Provider name: Keycloak (or any name you like that identify your
keycloak provider)</li>
<li>Auth Flow: OpenID Connect (authorization code flow)</li>
<li>Client ID: the same Client ID you entered when configuring the client
in Keycloak</li>
<li>Client Secret: found in keycloak on the client Credentials tab</li>
<li>Allowed: yes</li>
<li>Body: the link text to appear on the login page, such as Login with
Keycloak</li>
<li>Scope: openid email</li>
<li>Authentication URL: The “authorization_endpoint” URL found in the
OpenID Endpoint Configuration of your Keycloak realm</li>
<li>Token URL: The “token_endpoint” URL found in the OpenID Endpoint
Configuration of your Keycloak realm</li>
<li>JWKS URL: The “jwks_uri” URL found in the OpenID Endpoint
Configuration of your Keycloak realm</li>
</ul>
</div>
</div>
<div class="section" id="usage">
<h1><a class="toc-backref" href="#toc-entry-5">Usage</a></h1>
<p>On the login page, click on the authentication provider you configured.</p>
</div>
<div class="section" id="known-issues-roadmap">
<h1><a class="toc-backref" href="#toc-entry-6">Known issues / Roadmap</a></h1>
<ul class="simple">
<li>When going to the login screen, check for a existing token and do a
direct login without the clicking on the SSO link</li>
<li>When doing a logout an extra option to also logout at the SSO
provider.</li>
</ul>
</div>
<div class="section" id="changelog">
<h1><a class="toc-backref" href="#toc-entry-7">Changelog</a></h1>
<div class="section" id="section-1">
<h2><a class="toc-backref" href="#toc-entry-8">18.0.1.0.0 2024-10-09</a></h2>
<ul class="simple">
<li>Odoo 18 migration</li>
</ul>
</div>
<div class="section" id="section-2">
<h2><a class="toc-backref" href="#toc-entry-9">17.0.1.0.0 2024-03-20</a></h2>
<ul class="simple">
<li>Odoo 17 migration</li>
</ul>
</div>
<div class="section" id="section-3">
<h2><a class="toc-backref" href="#toc-entry-10">16.0.1.1.0 2024-02-28</a></h2>
<ul class="simple">
<li>Forward port OpenID Connect fixes from 15.0 to 16.0</li>
</ul>
</div>
<div class="section" id="section-4">
<h2><a class="toc-backref" href="#toc-entry-11">16.0.1.0.2 2023-11-16</a></h2>
<ul class="simple">
<li>Readme link updates</li>
</ul>
</div>
<div class="section" id="section-5">
<h2><a class="toc-backref" href="#toc-entry-12">16.0.1.0.1 2023-10-09</a></h2>
<ul class="simple">
<li>Add AzureAD code flow provider</li>
</ul>
</div>
<div class="section" id="section-6">
<h2><a class="toc-backref" href="#toc-entry-13">16.0.1.0.0 2023-01-27</a></h2>
<ul class="simple">
<li>Odoo 16 migration</li>
</ul>
</div>
<div class="section" id="section-7">
<h2><a class="toc-backref" href="#toc-entry-14">15.0.1.0.0 2023-01-06</a></h2>
<ul class="simple">
<li>Odoo 15 migration</li>
</ul>
</div>
<div class="section" id="section-8">
<h2><a class="toc-backref" href="#toc-entry-15">14.0.1.0.0 2021-12-10</a></h2>
<ul class="simple">
<li>Odoo 14 migration</li>
</ul>
</div>
<div class="section" id="section-9">
<h2><a class="toc-backref" href="#toc-entry-16">13.0.1.0.0 2020-04-10</a></h2>
<ul class="simple">
<li>Odoo 13 migration, add authorization code flow.</li>
</ul>
</div>
<div class="section" id="section-10">
<h2><a class="toc-backref" href="#toc-entry-17">10.0.1.0.0 2018-10-05</a></h2>
<ul class="simple">
<li>Initial implementation</li>
</ul>
</div>
</div>
<div class="section" id="bug-tracker">
<h1><a class="toc-backref" href="#toc-entry-18">Bug Tracker</a></h1>
<p>Bugs are tracked on <a class="reference external" href="https://github.com/OCA/server-auth/issues">GitHub Issues</a>.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
<a class="reference external" href="https://github.com/OCA/server-auth/issues/new?body=module:%20auth_oidc%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**">feedback</a>.</p>
<p>Do not contact contributors directly about support or help with technical issues.</p>
</div>
<div class="section" id="credits">
<h1><a class="toc-backref" href="#toc-entry-19">Credits</a></h1>
<div class="section" id="authors">
<h2><a class="toc-backref" href="#toc-entry-20">Authors</a></h2>
<ul class="simple">
<li>ICTSTUDIO</li>
<li>André Schenkels</li>
<li>ACSONE SA/NV</li>
</ul>
</div>
<div class="section" id="contributors">
<h2><a class="toc-backref" href="#toc-entry-21">Contributors</a></h2>
<ul class="simple">
<li>Alexandre Fayolle &lt;<a class="reference external" href="mailto:alexandre.fayolle&#64;camptocamp.com">alexandre.fayolle&#64;camptocamp.com</a>&gt;</li>
<li>Stéphane Bidoul &lt;<a class="reference external" href="mailto:stephane.bidoul&#64;acsone.eu">stephane.bidoul&#64;acsone.eu</a>&gt;</li>
<li>David Jaen &lt;<a class="reference external" href="mailto:david.jaen.revert&#64;gmail.com">david.jaen.revert&#64;gmail.com</a>&gt;</li>
<li>Andreas Perhab &lt;<a class="reference external" href="mailto:andreas.perhab&#64;wt-io-it.at">andreas.perhab&#64;wt-io-it.at</a>&gt;</li>
</ul>
</div>
<div class="section" id="maintainers">
<h2><a class="toc-backref" href="#toc-entry-22">Maintainers</a></h2>
<p>This module is maintained by the OCA.</p>
<a class="reference external image-reference" href="https://odoo-community.org">
<img alt="Odoo Community Association" src="https://odoo-community.org/logo.png" />
</a>
<p>OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.</p>
<p>Current <a class="reference external" href="https://odoo-community.org/page/maintainer-role">maintainer</a>:</p>
<p><a class="reference external image-reference" href="https://github.com/sbidoul"><img alt="sbidoul" src="https://github.com/sbidoul.png?size=40px" /></a></p>
<p>This module is part of the <a class="reference external" href="https://github.com/OCA/server-auth/tree/18.0/auth_oidc">OCA/server-auth</a> project on GitHub.</p>
<p>You are welcome to contribute. To learn how please visit <a class="reference external" href="https://odoo-community.org/page/Contribute">https://odoo-community.org/page/Contribute</a>.</p>
</div>
</div>
</div>
</body>
</html>
