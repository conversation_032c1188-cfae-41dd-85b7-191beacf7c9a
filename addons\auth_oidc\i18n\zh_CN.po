# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_oidc
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2024-07-03 10:47+0000\n"
"Last-Translator: x<PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.17\n"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__flow
msgid "Auth Flow"
msgstr "认证流程"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__client_secret
msgid "Client Secret"
msgstr "客户端密钥"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__code_verifier
msgid "Code Verifier"
msgstr "代码验证器"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__jwks_uri
msgid "JWKS URL"
msgstr "JWKS 网址"

#. module: auth_oidc
#: model:auth.oauth.provider,body:auth_oidc.provider_azuread_multi
#: model:auth.oauth.provider,body:auth_oidc.provider_azuread_single
msgid "Log in with Microsoft"
msgstr "使用 Microsoft 登录"

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__access_token
msgid "OAuth2"
msgstr "OAuth2"

#. module: auth_oidc
#: model:ir.model,name:auth_oidc.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr "OAuth2 提供者"

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__id_token_code
msgid "OpenID Connect (authorization code flow)"
msgstr "OpenID Connect（授权码流程）"

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__id_token
msgid "OpenID Connect (implicit flow, not recommended)"
msgstr "OpenID Connect（隐式流程，不推荐）"

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__token_endpoint
msgid "Required for OpenID Connect authorization code flow."
msgstr "OpenID Connect 授权码流程所需。"

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__jwks_uri
msgid "Required for OpenID Connect."
msgstr "OpenID Connect 所需。"

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__token_map
msgid ""
"Some Oauth providers don't map keys in their responses exactly as required.  "
"It is important to ensure user_id and email at least are mapped. For OpenID "
"Connect user_id is the sub key in the standard."
msgstr ""
"一些 OAuth 提供者在其响应中并没有完全按照要求映射键。至少需要确保 user_id 和 "
"email 被映射。对于 OpenID Connect，user_id 是标准中的 sub 键。"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__token_map
msgid "Token Map"
msgstr "令牌映射"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__token_endpoint
msgid "Token URL"
msgstr "令牌网址"

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__code_verifier
msgid "Used for PKCE."
msgstr "用于 PKCE。"

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__client_secret
msgid ""
"Used in OpenID Connect authorization code flow for confidential clients."
msgstr "在 OpenID Connect 授权码流程中用于保密客户端。"

#. module: auth_oidc
#: model:ir.model,name:auth_oidc.model_res_users
msgid "User"
msgstr "用户"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__validation_endpoint
msgid "UserInfo URL"
msgstr "用户信息网址"

#. module: auth_oidc
#: model_terms:ir.ui.view,arch_db:auth_oidc.view_oidc_provider_form
msgid "e.g from:to upn:email sub:user_id"
msgstr "例如 from:to upn:email sub:user_id"

#. module: auth_oidc
#: model:auth.oauth.provider,body:auth_oidc.local_keycloak
msgid "keycloak:8080 on localhost"
msgstr "localhost 上的 keycloak:8080"
