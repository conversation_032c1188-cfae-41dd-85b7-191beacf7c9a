# Changelog

## 18.0.1.7

Improve the model (fields) translations with English/en_US (if enabled) reversed translations, in case en_US is not the primary language.

## 18.0.1.6

In the form builder:
- Add configurable Model (fields) translations.\
  Eg useful in select components with the URL Data Source and the getData API.
- Improve info in Translations tab.

## 18.0.1.5

Fixes for public form:
- Fix response for public submission endpoint.
- Fix `_generate_odoo_domain` method for public form.

## 18.0.1.4

Change `formio.form` method `_generate_odoo_domain` to provide the `formio.form` record in args.

## ********

Fix (migration 18) `copy` method in models:
- `formio.builder`
- `ir_actions`
- `ir_attachment`

Since Odoo 18 the `copy` method applies on a multi record set.

## ********

Ensure sufficient formio.js versions (GitHub tags) are downloaded and registered.\
In future versions this will be more configurable.

## ********

Possibility to override the form submit (input) value, by slurping from the input (DOM) element value.\
This is especially useful for external JavaScript (scripts) that modify DOM input elements.

## ********

Initial 18.0 release.
