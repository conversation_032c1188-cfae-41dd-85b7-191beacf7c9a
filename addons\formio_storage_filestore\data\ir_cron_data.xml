<?xml version="1.0"?>
<!-- Copyright Nova Code (https://www.novacode.nl)
License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl.html) -->

<odoo>
    <data noupdate="1">
        <record id="ir_cron_formio_storage_filestore_unlink_pending_attachments" forcecreate="True" model="ir.cron">
            <field name="name">Forms | Storage Filestore: Unlink pending Attachments for Forms never submitted</field>
            <field name="model_id" ref="model_ir_attachment"/>
            <field name="state">code</field>
            <field name="code">model.cron_formio_storage_filestore_unlink_pending_attachments()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
        </record>
    </data>
</odoo>
