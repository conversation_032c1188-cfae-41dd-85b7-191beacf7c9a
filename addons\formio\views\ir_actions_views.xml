<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_server_action_form" model="ir.ui.view">
        <field name="model">ir.actions.server</field>
        <field name="inherit_id" ref="base.view_server_action_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='model_id']" position="after">
                <field name="formio_form_execute_after_action" invisible="model_name != 'formio.form'"/>
                <div class="mb-2 text-muted" colspan="2" invisible="model_name != 'formio.form'">
                    <i class="fa fa-info-circle" title="info"/> Forms Execute After: If assigned in a Form Builder (Actions API), this sever action will be executed.
                </div>
                <field name="formio_ref"
                       invisible="model_name != 'formio.form'"
                       required="model_name == 'formio.form'"/>
                <div class="mb-2 text-muted" colspan="2" invisible="model_name != 'formio.form'">
                    <i class="fa fa-info-circle" title="info"/> Forms Ref: Reference used in Form Builder(s) to indentiy the link between a Server Action and Form Builder(s).
                </div>
                <field name="formio_builder_ids"
                       widget="many2many_tags"
                       string="Form Builders"
                       invisible="model_name != 'formio.form'"
                       optional="show"
                />
                <div class="mb-2 text-muted" colspan="2" invisible="model_name != 'formio.form'">
                    <i class="fa fa-info-circle" title="info"/> Form Builders: Be careful about ordering the sequence of a server action in a Form Builder, if other Form Builders use the same server action.
                </div>
            </xpath>
        </field>
    </record>

    <record id="view_server_action_tree" model="ir.ui.view">
        <field name="model">ir.actions.server</field>
        <field name="inherit_id" ref="base.view_server_action_tree"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="formio_form_execute_after_action" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='state']" position="after">
                <field name="formio_builder_ids" widget="many2many_tags" string="Form Builders" optional="show"/>
                <field name="formio_ref" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
