<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (https://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <record id="view_formio_form_tree" model="ir.ui.view">
        <field name="name">formio.form.list</field>
        <field name="model">formio.form</field>
        <field name="arch" type="xml">
            <list string="Form Submissions"
                  decoration-info="state in ('DRAFT')"
                  decoration-success="state in ('COMPLETE')"
                  decoration-danger="state in ('ERROR')"
                  decoration-muted="state in ('CANCEL')">
                <field name="id"/>
                <field name="uuid" optional="hide"/>
                <field name="sequence" optional="hide"/>
                <field name="title"/>
                <field name="builder_id"/>
                <field name="user_id"/>
                <field name="submission_partner_id"/>
                <field name="submission_commercial_partner_id" optional="hide"/>
                <field name="submission_date"/>
                <field name="state"/>
                <button
                    string="Open Form"
                    name="action_view_formio"
                    type="object"
                    class="btn-sm btn-primary"
                    icon="fa-arrow-right"/>
                <field name="portal_share" optional="show"/>
                <field name="public_share" optional="show"/>
                <field name="public_access_rule_type" optional="hide"/>
                <field name="public_access" optional="show"/>
                <field name="public_create" optional="hide"/>
                <field name="res_model_name" string="Resource Type" optional="show"/>
                <field name="initial_res_model_name" string="Resource Type #1" optional="hide"/>
                <field name="res_name" optional="show"/>
                <field name="res_partner_id" optional="show"/>
                <field name="write_date" optional="hide"/>
                <field name="create_date" optional="hide"/>
            </list>
        </field>
    </record>

    <record id="view_formio_form_kanban" model="ir.ui.view">
        <field name="name">formio.form.kanban</field>
        <field name="model">formio.form</field>
        <field name="arch" type="xml">
            <kanban class="oe_background_grey o_kanban_dashboard" default_group_by="kanban_group_state" quick_create="false">
                <field name="kanban_group_state"/>
                <field name="res_model_id"/>
                <field name="res_id"/>
                <field name="res_act_window_url"/>
                <field name="user_id"/>
                <field name="portal"/>
                <field name="public"/>
                <templates>
                    <t t-name="card">
                        <div class="row">
                            <field class="fw-bolder" name="title"/>
                        </div>
                        <div class="row">
                            <div class="mt8">
                                <button name="action_view_formio" type="object" role="button" class="btn btn-primary">Form</button>
                            </div>
                        </div>
                        <t t-if="record.res_model_id.raw_value and record.res_id.raw_value">
                            <div class="row">
                                <div class="col-xs-12">
                                    <t t-if="record.res_name.raw_value"><field name="res_name"/>: </t><a class="btn btn-default" t-att-href="record.res_act_window_url.raw_value" role="button"><field name="res_model_name"/></a>
                                </div>
                            </div>
                        </t>
                        <!-- submission info -->
                        <t t-if="record.submission_partner_id.raw_value">
                            <strong>Submission:</strong>
                            <div>
                                <div>
                                    <i class="fa fa-user" title="Submission partner" aria-label="Submission partner"/> <field name="submission_partner_id"/>
                                </div>
                            </div>
                            <div>
                                <div>
                                    <i class="fa fa-clock-o" title="Submission date" aria-label="Submission date"/> <field name="submission_date"/>
                                </div>
                            </div>
                            <div>
                                <div>
                                    <span>State: <field name="state"/></span>
                                </div>
                            </div>
                        </t>
                        <footer>
                            <small class="text-muted">ID: <field name="id"/></small>
                            <div style="margin-left: 4px;" invisible="portal == False">
                                <i class="fa fa-globe text-muted"/><span class="text-muted"> portal</span>
                            </div>
                            <div class="d-flex mt-auto">
                                <field name="activity_ids" widget="kanban_activity"/>
                            </div>
                            <field name="user_id" widget="many2one_avatar_user" domain="[('share', '=', False)]" class="ms-auto"/>
                        </footer>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_formio_form_formio" model="ir.ui.view">
        <field name="name">formio.form.formio</field>
        <field name="model">formio.form</field>
        <field name="arch" type="xml">
            <formio_form string="formio Form">
                <field name="id"/>
                <field name="uuid"/>
                <field name="name"/>
                <field name="title"/>
                <field name="state"/>
                <field name="display_state"/>
                <field name="submission_partner_name"/>
                <field name="submission_date"/>
                <field name="submission_data"/>
                <field name="iframe_resizer_body_margin"/>
                <field name="full_width"/>
            </formio_form>
        </field>
    </record>

    <record id="view_formio_form_form" model="ir.ui.view">
        <field name="name">formio.form.form</field>
        <field name="model">formio.form</field>
        <field name="arch" type="xml">
	    <form string="Form">
                <header>
                    <button
                        name="action_send_invitation_mail" type="object" string="Send Invitation Mail"
                        groups="formio.group_formio_user_all_forms"
                        invisible="user_id == False or id == False or state not in ['PENDING', 'DRAFT']"
                        class="btn-primary"/>
                    <button
                        name="action_draft" type="object" string="Draft"
                        invisible="state in ['PENDING', 'DRAFT'] or allow_force_update_state == False"/>
                    <button
                        name="action_complete" type="object" string="Complete"
                        invisible="state != 'DRAFT' or allow_force_update_state == False"/>
                    <button
                        name="action_cancel" type="object" string="Cancel"
                        invisible="state == 'CANCEL' or allow_force_update_state == False"/>
                    <button
                        name="action_copy_to_current" type="object" string="Copy To Current"
                        invisible="allow_copy == False or copy_to_current == False"/>
                    <field name="state" widget="statusbar" statusbar_visible="PENDING,DRAFT,COMPLETE,CANCEL" invisible="state == 'ERROR'"/>
                    <field name="state" widget="statusbar" statusbar_visible="PENDING,DRAFT,ERROR,COMPLETE,CANCEL" invisible="state != 'ERROR'"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_formio"
                                type="object"
                                string="Form"
                                aria-label="Form"
                                class="oe_stat_button bg-primary formio_form"
                                icon="fa-rocket">
                            <div class="o_stat_info o_field_widget">
                                <span>Form</span>
                            </div>
                        </button>
                        <button name="action_open_res_act_window" type="object"
                                invisible="res_id == False or res_id == 0"
                                class="oe_stat_button" icon="fa-link" aria-label="Resource">
                            <field name="res_name"/>
                        </button>
                    </div>
                    <div class="oe_title mb24">
                        <h1>
                            <field name="title"
                                   class="text-break"
                                   placeholder="Insert a meaningful title here"
                                   invisible="builder_id == False"/>
                        </h1>
                    </div>
                    <group name="general">
                        <group>
                            <field name="builder_id" readonly="id != False" domain="builder_id_domain"/>
                            <field name="builder_id_domain" invisible="1"/>
                            <field name="res_name" string="Resource" invisible="res_name == False"/>
                        </group>
                        <group>
                            <field name="id" invisible="id == False"/>
                            <field name="uuid"/>
                            <field name="res_id" invisible="1"/>
                            <field name="allow_force_update_state" invisible="1"/>
                        </group>
                    </group>
                    <group name="assignment_and_submission">
                        <group invisible="builder_id == False">
                            <field name="invitation_mail_template_id" invisible="1"/>
                            <field name="user_id" groups="formio.group_formio_user_all_forms" readonly="state in ['COMPLETE', 'CANCEL']"/>
                        </group>
                        <group invisible="builder_id == False">
                            <field name="submission_partner_id" string="Submission Partner"/>
                            <field name="submission_commercial_partner_id" invisible="submission_commercial_partner_id == submission_partner_id"/>
                            <field name="submission_user_id" groups="base.group_erp_manager" string="Submission User"/>
                            <field name="submission_date" string="Submission Date"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Settings" name="settings">
                            <group>
                                <group name="access_settings" string="Access">
                                    <field name="public_share" groups="formio.group_formio_admin"/>
                                    <field name="public_access_rule_type"/>
                                    <field name="public_access_date_from"
                                           groups="formio.group_formio_admin"
                                           invisible="public_access_rule_type != 'time_interval'"
                                           required="public_share == True"/>
                                    <label for="public_access_interval_number"
                                           string="Public Access Expire"
                                           invisible="public_access_rule_type != 'time_interval'"/>
                                    <div invisible="public_access_rule_type != 'time_interval'">
                                        <field name="public_access_interval_number"
                                               class="oe_inline"
                                               groups="formio.group_formio_admin"
                                               required="public_share == True"/>
                                        <field name="public_access_interval_type"
                                               class="oe_inline ml-2"
                                               groups="formio.group_formio_admin"
                                               required="public_share == True"/>
                                    </div>
                                    <field name="public_access" groups="formio.group_formio_admin"/>
                                </group>
                                <group name="actions_settings" string="Actions">
                                    <field name="allow_copy"/>
                                    <field name="copy_to_current"
                                           invisible="allow_copy == False"/>
                                </group>
                                <group string="Display" name="display_settings" col="4">
                                    <field name="show_title" groups="formio.group_formio_admin" invisible="builder_id == False"/>
                                    <field name="show_id" string="Show ID" groups="formio.group_formio_admin" invisible="builder_id == False"/>
                                    <field name="show_state" groups="formio.group_formio_admin" invisible="builder_id == False"/>
                                    <field name="show_uuid" string="Show UUID" groups="formio.group_formio_admin" invisible="builder_id == False"/>
                                    <field name="show_user_metadata" groups="formio.group_formio_admin" invisible="builder_id == False"/>
                                </group>
                            </group>
                        </page>
                        <page string="Submission Data" name="data" groups="formio.group_formio_admin">
                            <field name="submission_data" readonly="readonly_submission_data == True"/>
                            <field name="readonly_submission_data" invisible="1"/>
                            <field name="id" invisible="1"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="view_formio_form_search" model="ir.ui.view">
        <field name="name">formio.form.search</field>
        <field name="model">formio.form</field>
        <field name="arch" type="xml">
            <search string="Form Submissions">
                <field name="id"/>
                <field name="title"/>
                <field name="builder_id"/>
                <field name="uuid"/>
                <field name="user_id" groups="base.group_erp_manager"/>
                <field name="submission_partner_id"/>
                <field name="submission_user_id" groups="base.group_erp_manager"/>
                <field name="res_name" string="Resource Name"/>
                <field name="res_partner_id"/>
                <field name="res_model_id"/>
                <field name="res_id"/>
                <field name="initial_res_model_id"/>
                <field name="initial_res_id"/>
                <separator/>
                <filter string="My Forms" name="my_forms"
                        domain="[('create_uid', '=', uid)]"/>
                <filter string="Assigned Forms" name="assigned_forms"
                        domain="[('user_id', '=', uid)]"/>
                <separator/>
                <filter string="Pending" name="state_pending"
                        domain="[('state', '=', 'PENDING')]"/>
                <filter string="Draft" name="state_draft"
                        domain="[('state', '=', 'DRAFT')]"/>
                <filter string="Completed" name="state_complete"
                        domain="[('state', '=', 'COMPLETE')]"/>
                <filter string="Canceled" name="state_cancel"
                        domain="[('state', '=', 'CANCEL')]"/>
                <group expand="0" string="Group By">
                    <filter string="Title" name="title" domain="[]" context="{'group_by':'title'}"/>
                    <filter string="Form Builder" name="builder_id" domain="[]" context="{'group_by':'builder_id'}"/>
                    <filter string="State" name="state" domain="[]" context="{'group_by':'state'}"/>
                    <filter string="Resource Name" name="res_name" domain="[]" context="{'group_by':'res_name'}"/>
                    <filter string="Assigned User" name="user" domain="[]" context="{'group_by':'user_id'}"/>
                    <filter string="Submission Date" name="submission_date" domain="[]" context="{'group_by':'submission_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- actions -->
    <record id="action_formio_form" model="ir.actions.act_window">
        <field name="name">Form Submissions</field>
        <field name="res_model">formio.form</field>
        <field name="view_mode">list,kanban,form,formio_form</field>
    </record>

    <record id="action_formio_form_view_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="0"/>
        <field name="view_mode">list</field>
        <field name="view_id" ref="formio.view_formio_form_tree"/>
        <field name="act_window_id" ref="action_formio_form"/>
    </record>
    <record id="action_formio_form_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="formio.view_formio_form_kanban"/>
        <field name="act_window_id" ref="action_formio_form"/>
    </record>
    <record id="action_formio_form_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="formio.view_formio_form_form"/>
        <field name="act_window_id" ref="action_formio_form"/>
    </record>
    <record id="action_formio_form_view_formio" model="ir.actions.act_window.view">
        <field name="sequence" eval="3"/>
        <field name="view_mode">formio_form</field>
        <field name="view_id" ref="formio.view_formio_form_formio"/>
        <field name="act_window_id" ref="action_formio_form"/>
    </record>
</odoo>
