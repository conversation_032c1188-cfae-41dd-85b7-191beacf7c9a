=============================
Authentication OpenID Connect
=============================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:cd754fc72d2039d02ab1b8aec98af43fb9543c9a70f2150ab6e482954e4e83d6
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fserver--auth-lightgray.png?logo=github
    :target: https://github.com/OCA/server-auth/tree/18.0/auth_oidc
    :alt: OCA/server-auth
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/server-auth-18-0/server-auth-18-0-auth_oidc
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/server-auth&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module allows users to login through an OpenID Connect provider
using the authorization code flow or implicit flow.

Note the implicit flow is not recommended because it exposes access
tokens to the browser and in http logs.

**Table of contents**

.. contents::
   :local:

Installation
============

This module depends on the
`python-jose <https://pypi.org/project/python-jose/>`__ library, not to
be confused with ``jose`` which is also available on PyPI.

Configuration
=============

Setup for Microsoft Azure
-------------------------

Example configuration with OpenID Connect authorization code flow.

1. configure a new web application in Azure with OpenID and code flow
   (see the `provider
   documentation <https://docs.microsoft.com/en-us/powerapps/maker/portals/configure/configure-openid-provider>`__))

2. in this application the redirect url must be be "<url of your
   server>/auth_oauth/signin" and of course this URL should be reachable
   from Azure

3. create a new authentication provider in Odoo with the following
   parameters (see the `portal
   documentation <https://docs.microsoft.com/en-us/powerapps/maker/portals/configure/configure-openid-settings>`__
   for more information):

|image|

|image1|

Single tenant provider limits the access to user of your tenant, while
Multitenants allow access for all AzureAD users, so user of foreign
companies can use their AzureAD login without an guest account.

-  Provider Name: Azure AD Single Tenant
-  Client ID: Application (client) id
-  Client Secret: Client secret
-  Allowed: yes

or

-  Provider Name: Azure AD Multitenant
-  Client ID: Application (client) id
-  Client Secret: Client secret
-  Allowed: yes
-  replace {tenant_id} in urls with your Azure tenant id

|image2|

Setup for Keycloak
------------------

Example configuration with OpenID Connect authorization code flow.

In Keycloak:

1. configure a new Client
2. make sure Authorization Code Flow is Enabled.
3. configure the client Access Type as "confidential" and take note of
   the client secret in the Credentials tab
4. configure the redirect url to be "<url of your
   server>/auth_oauth/signin"

In Odoo, create a new Oauth Provider with the following parameters:

-  Provider name: Keycloak (or any name you like that identify your
   keycloak provider)
-  Auth Flow: OpenID Connect (authorization code flow)
-  Client ID: the same Client ID you entered when configuring the client
   in Keycloak
-  Client Secret: found in keycloak on the client Credentials tab
-  Allowed: yes
-  Body: the link text to appear on the login page, such as Login with
   Keycloak
-  Scope: openid email
-  Authentication URL: The "authorization_endpoint" URL found in the
   OpenID Endpoint Configuration of your Keycloak realm
-  Token URL: The "token_endpoint" URL found in the OpenID Endpoint
   Configuration of your Keycloak realm
-  JWKS URL: The "jwks_uri" URL found in the OpenID Endpoint
   Configuration of your Keycloak realm

.. |image| image:: https://raw.githubusercontent.com/OCA/server-auth/18.0/auth_oidc/static/description/oauth-microsoft_azure-api_permissions.png
.. |image1| image:: https://raw.githubusercontent.com/OCA/server-auth/18.0/auth_oidc/static/description/oauth-microsoft_azure-optional_claims.png
.. |image2| image:: https://raw.githubusercontent.com/OCA/server-auth/18.0/auth_oidc/static/description/odoo-azure_ad_multitenant.png

Usage
=====

On the login page, click on the authentication provider you configured.

Known issues / Roadmap
======================

-  When going to the login screen, check for a existing token and do a
   direct login without the clicking on the SSO link
-  When doing a logout an extra option to also logout at the SSO
   provider.

Changelog
=========

********.0 2024-10-09
---------------------

-  Odoo 18 migration

********.0 2024-03-20
---------------------

-  Odoo 17 migration

********.0 2024-02-28
---------------------

-  Forward port OpenID Connect fixes from 15.0 to 16.0

********.2 2023-11-16
---------------------

-  Readme link updates

********.1 2023-10-09
---------------------

-  Add AzureAD code flow provider

********.0 2023-01-27
---------------------

-  Odoo 16 migration

********.0 2023-01-06
---------------------

-  Odoo 15 migration

********.0 2021-12-10
---------------------

-  Odoo 14 migration

********.0 2020-04-10
---------------------

-  Odoo 13 migration, add authorization code flow.

10.0.1.0.0 2018-10-05
---------------------

-  Initial implementation

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/server-auth/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/server-auth/issues/new?body=module:%20auth_oidc%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* ICTSTUDIO
* André Schenkels
* ACSONE SA/NV

Contributors
------------

-  Alexandre Fayolle <<EMAIL>>
-  Stéphane Bidoul <<EMAIL>>
-  David Jaen <<EMAIL>>
-  Andreas Perhab <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-sbidoul| image:: https://github.com/sbidoul.png?size=40px
    :target: https://github.com/sbidoul
    :alt: sbidoul

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-sbidoul| 

This module is part of the `OCA/server-auth <https://github.com/OCA/server-auth/tree/18.0/auth_oidc>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
