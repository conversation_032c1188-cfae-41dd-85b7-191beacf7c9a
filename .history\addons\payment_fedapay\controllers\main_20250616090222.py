# Part of Odoo. See LICENSE file for full copyright and licensing details.


import logging


from werkzeug.exceptions import Forbidden

from odoo import _, http
from odoo.exceptions import ValidationError
from odoo.http import request

from odoo.addons.payment import utils as payment_utils
from odoo.addons.payment_fedapay import const


_logger = logging.getLogger(__name__)


class PaypalController(http.Controller):
    _complete_url = '/payment/fedapay/complete_order'

    @http.route(_complete_url, type='json', auth='public', methods=['POST'])
    def paypal_complete_order(self, provider_id, order_id, reference=None):
        """ Make a capture request and handle the notification data.

        :param int provider_id: The provider handling the transaction, as a `payment.provider` id.
        :param string order_id: The order id provided by FedaPay to identify the order.
        :param str reference: The reference of the transaction used to generate idempotency key.
        :return: None
        """

        if reference:
            tx_sudo = request.env['payment.transaction'].sudo()._get_tx_from_notification_data(
                'fedapay', {'reference_id': reference}
            )
        response = dict()
    
        tx_sudo._handle_notification_data('fedapay', response)
