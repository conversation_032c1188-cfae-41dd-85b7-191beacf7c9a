from odoo import http
from odoo.http import request
from urllib.parse import urlparse, parse_qsl
from odoo.addons.auth_oauth.controllers.main import OAuthLogin
from werkzeug.utils import redirect as werkzeug_redirect
import logging

_logger = logging.getLogger(__name__)

class OAuthAutoLoginRedirect(OAuthLogin):

    @http.route('/web/login', type='http', auth='none')
    def web_login(self, redirect=None, **kwargs):
        # Si on passe ?no_autologin, on l'active en session pour cette tentative
        if "no_autologin" in kwargs or "oauth_error" in kwargs or "error" in kwargs:
            request.session['no_autologin'] = True
            return super().web_login(redirect=redirect, **kwargs)

        # Si no_autologin actif, afficher login interne
        if request.session.pop('no_autologin', False):
            # pop() supprime le flag dès qu'il est lu
            return super().web_login(redirect=redirect, **kwargs)

        # Providers avec autologin
        providers = [
            p for p in self.list_providers()
            if p.get("autologin") and p.get("enabled")
        ]
        if len(providers) != 1:
            return super().web_login(redirect=redirect, **kwargs)

        provider = providers[0]

        # Vérification si redirect contient un flag no_autologin
        if redirect:
            url = urlparse(redirect)
            params = dict(parse_qsl(url.query))
            if "no_autologin" in params or "oauth_error" in params or "error" in params:
                request.session['no_autologin'] = True
                return super().web_login(redirect=redirect, **kwargs)

        _logger.info('Redirecting to: %s', provider["auth_link"])
        return werkzeug_redirect(provider["auth_link"])
