<section class="oe_container">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;"><b>Forms &bull; File Storage</h2>
    <h3 class="oe_slogan mt8">File (upload) storage in Odoo filestore as attachments</h3>
    <h3 class="oe_slogan">
      <small>Store file uploads (authenticated, public) in Forms as its attachments</small>
    </h3>
    <div class="oe_row">
      <h5>Installation Requirements</h5>
      <ul>
        <li>Forms (<code>formio</code>) module version >= 8.15</li>
        <li>
          PyPi package <code>formio-data</code> version >= 0.3.8<br/>
          <code>pip3 install -U formio-data</code>
        </li>
      </ul>
    </div>
</section>

<section class="oe_container oe_dark">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;">Form Builder</h2>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Configure (default) JS Options</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="formio_builder_js_options.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">Add and configure File Component</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="formio_builder_file_component.png">
    </div>
  </div>
</section>

<section class="oe_container">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;">Form</h2>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">File component - Upload single or multiple files</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="formio_form_file_multiple_uploads.png">
    </div>
    <div class="oe_row_img oe_centered">
      <h4 class="oe_mb16 text-center">File upload(s) stored as Attachments</h4>
      <img class="oe_demo oe_picture oe_screenshot" src="formio_form_attachments.png">
    </div>
  </div>
</section>

<section class="oe_container oe_dark">
  <div class="oe_row oe_centeralign mb16">
    <h4>Visit the online Demo</h4>
    <p>Click the <strong>Live Preview</strong> button in the top.</p>
    <p class="mt8"><strong>Users</strong> (email / password)</p>
    <ul>
      <li>demo / demo</li>
      <li>portal / portal</li>
    </ul>
  </div>
</section>
