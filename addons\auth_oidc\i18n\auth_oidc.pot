# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_oidc
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__flow
msgid "Auth Flow"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__client_secret
msgid "Client Secret"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__code_verifier
msgid "Code Verifier"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__jwks_uri
msgid "JWKS URL"
msgstr ""

#. module: auth_oidc
#: model:auth.oauth.provider,body:auth_oidc.provider_azuread_multi
#: model:auth.oauth.provider,body:auth_oidc.provider_azuread_single
msgid "Log in with Microsoft"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__access_token
msgid "OAuth2"
msgstr ""

#. module: auth_oidc
#: model:ir.model,name:auth_oidc.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__id_token_code
msgid "OpenID Connect (authorization code flow)"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields.selection,name:auth_oidc.selection__auth_oauth_provider__flow__id_token
msgid "OpenID Connect (implicit flow, not recommended)"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__token_endpoint
msgid "Required for OpenID Connect authorization code flow."
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__jwks_uri
msgid "Required for OpenID Connect."
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__token_map
msgid ""
"Some Oauth providers don't map keys in their responses exactly as required."
"  It is important to ensure user_id and email at least are mapped. For "
"OpenID Connect user_id is the sub key in the standard."
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__token_map
msgid "Token Map"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__token_endpoint
msgid "Token URL"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__code_verifier
msgid "Used for PKCE."
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,help:auth_oidc.field_auth_oauth_provider__client_secret
msgid ""
"Used in OpenID Connect authorization code flow for confidential clients."
msgstr ""

#. module: auth_oidc
#: model:ir.model,name:auth_oidc.model_res_users
msgid "User"
msgstr ""

#. module: auth_oidc
#: model:ir.model.fields,field_description:auth_oidc.field_auth_oauth_provider__validation_endpoint
msgid "UserInfo URL"
msgstr ""

#. module: auth_oidc
#: model_terms:ir.ui.view,arch_db:auth_oidc.view_oidc_provider_form
msgid "e.g from:to upn:email sub:user_id"
msgstr ""

#. module: auth_oidc
#: model:auth.oauth.provider,body:auth_oidc.local_keycloak
msgid "keycloak:8080 on localhost"
msgstr ""
