#!/bin/bash
set -e

# Variables par défaut si non définies dans docker-compose
: ${HOST:='mydb'}
: ${PORT:=5432}
: ${USER:='odoo'}
: ${PASSWORD:='myodoo'}

ODOO_RC=/etc/odoo/odoo.conf

# Installation des dépendances Python
if [ -f /etc/odoo/requirements.txt ]; then
    echo "📦 Installation des dépendances Python..."
    pip3 install --no-cache-dir -r /etc/odoo/requirements.txt
fi

# Préparer les paramètres DB
DB_ARGS=()
function check_config() {
    param="$1"
    value="$2"
    if grep -q -E "^\s*\b${param}\b\s*=" "$ODOO_RC" ; then       
        value=$(grep -E "^\s*\b${param}\b\s*=" "$ODOO_RC" |cut -d " " -f3|sed 's/["\n\r]//g')
    fi
    DB_ARGS+=("--${param}")
    DB_ARGS+=("${value}")
}

check_config "db_host" "$HOST"
check_config "db_port" "$PORT"
check_config "db_user" "$USER"
check_config "db_password" "$PASSWORD"

# Attendre que PostgreSQL soit prêt
echo "⏳ Attente de la base de données..."
wait-for-psql.py ${DB_ARGS[@]} --timeout=30

# Lancer Odoo
echo "🚀 Démarrage d'Odoo..."
exec odoo "$@" "${DB_ARGS[@]}"
