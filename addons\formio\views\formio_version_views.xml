<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <record id="view_formio_version_tree" model="ir.ui.view">
        <field name="name">formio.version.list</field>
        <field name="model">formio.version</field>
        <field name="arch" type="xml">
            <list string="Version">
                <field name="name"/>
                <field name="css_assets"/>
                <field name="js_assets"/>
                <field name="active" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <record id="view_formio_version_form" model="ir.ui.view">
        <field name="name">formio.version.form</field>
        <field name="model">formio.version</field>
        <field name="arch" type="xml">
	    <form string="Form">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_formio_version_translation)d" type="action" string="Translations"
                                context="{'default_formio_version_id': id, 'search_default_formio_version_id': id}"
                                groups="formio.group_formio_admin" icon="fa-language"/>
                    </div>
                    <label for="name" class="oe_edit_only"/>
                    <h1><field name="name"/></h1>
                    <group>
                        <field name="description"/>
                        <field name="active"/>
                    </group>
                    <notebook>
                        <page string="Translations" name="translations" groups="formio.group_formio_admin">
                            <button string="Add Base Translations" name="action_add_base_translations" type="object" class="btn-primary"/>
                            <button string="Unlink Base Translations" name="action_unlink_base_translations" type="object" confirm="Are you sure to unlink the base translations from this formio.js version?"/>
                            <div class="text-muted mt-4 mb-4">
                                <i class="fa fa-info-circle" title="info"/> The Base Translations can be found in menu: Configuration / Translations.<br/>
                                <i class="fa fa-info-circle" title="info"/> Action button ADD BASE TRANSLATIONS doesn't overwrite changes.<br/>
                                <i class="fa fa-info-circle" title="info"/> Action button UNLINK BASE TRANSLATIONS removes the Base Translations from this formio.js version, but not the custom ones.
                            </div>
                            <field name="translation_ids" context="{'default_formio_version_id': id}">
                                <list editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="lang_id" options="{'no_quick_create':True,'no_create_edit':True}"/>
                                    <field name="source_property"/>
                                    <field name="source_text"/>
                                    <field name="value"/>
                                    <field name="base_translation_origin" optional="show"/>
                                    <field name="base_translation_updated" optional="show"/>
                                </list>
                            </field>
                        </page>
                        <page string="Assets (JavaScript, CSS, Fonts)" name="assets" groups="formio.group_formio_admin">
                            <field name="assets" context="{'default_version_id': id}">
                                <list>
                                    <field name="sequence" widget="handle"/>
                                    <field name="attachment_id"/>
                                    <field name="type"/>
                                    <field name="attachment_type"/>
                                    <field name="attachment_formio_ref" optional="show"/>
                                </list>
                                <form>
                                    <group>
                                        <field name="version_id" readonly="1" invisible="version_id == False"/>
                                        <field name="type"/>
                                        <field name="attachment_id"/>
                                        <field name="attachment_type" invisible="attachment_id == False"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page string="License(s)" name="others" groups="formio.group_formio_admin">
                            <field name="license_assets" context="{'default_version_id': id}">
                                <list create="false" edit="false" delete="false">
                                    <field name="attachment_id"/>
                                    <field name="type"/>
                                    <field name="attachment_type"/>
                                </list>
                                <form>
                                    <group>
                                        <field name="version_id" readonly="1" invisible="version_id == False"/>
                                        <field name="type"/>
                                        <field name="attachment_id"/>
                                        <field name="attachment_type" invisible="attachment_id == False"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="view_formio_version_search" model="ir.ui.view">
        <field name="name">formio.version.search</field>
        <field name="model">formio.version</field>
        <field name="arch" type="xml">
            <search string="Formio Versions">
                <field name="name"/>
                <separator/>
                <filter string="Archived" name="not_active"
                        domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="action_formio_version" model="ir.actions.act_window">
        <field name="name">Installed formio.js versions</field>
        <field name="res_model">formio.version</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="view_formio_version_tree"/>
    </record>
</odoo>
