<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <data>
        <record id="i18n_complete" model="formio.translation.source">
            <field name="property">complete</field>
            <field name="source">Submission Complete</field>
        </record>
        <record id="i18n_error" model="formio.translation.source">
            <field name="property">error</field>
            <field name="source">Please fix the following errors before submitting.</field>
        </record>
        <record id="i18n_alertMessage" model="formio.translation.source">
            <field name="property">alertMessage</field>
            <field name="source">{{message}}</field>
        </record>
        <record id="i18n_required" model="formio.translation.source">
            <field name="property">required</field>
            <field name="source">{{field}} is required</field>
        </record>
        <record id="i18n_pattern" model="formio.translation.source">
            <field name="property">pattern</field>
            <field name="source">{{field}} does not match the pattern {{pattern}}</field>
        </record>
        <record id="i18n_minLength" model="formio.translation.source">
            <field name="property">minLength</field>
            <field name="source">{{field}} must be longer than {{length}} characters.</field>
        </record>
        <record id="i18n_maxLength" model="formio.translation.source">
            <field name="property">maxLength</field>
            <field name="source">{{field}} must be shorter than {{length}} characters.</field>
        </record>
        <record id="i18n_min" model="formio.translation.source">
            <field name="property">min</field>
            <field name="source">{{field}} cannot be less than {{min}}.</field>
        </record>
        <record id="i18n_max" model="formio.translation.source">
            <field name="property">max</field>
            <field name="source">{{field}} cannot be greater than {{max}}.</field>
        </record>
        <record id="i18n_maxDate" model="formio.translation.source">
            <field name="property">maxDate</field>
            <field name="source">{{field}} should not contain date after {{- maxDate}}</field>
        </record>
        <record id="i18n_minDate" model="formio.translation.source">
            <field name="property">minDate</field>
            <field name="source">{{field}} should not contain date before {{- minDate}}</field>
        </record>
        <record id="i18n_invalid_email" model="formio.translation.source">
            <field name="property">invalid_email</field>
            <field name="source">{{field}} must be a valid email.</field>
        </record>
        <record id="i18n_invalid_url" model="formio.translation.source">
            <field name="property">invalid_url</field>
            <field name="source">{{field}} must be a valid url.</field>
        </record>
        <record id="i18n_invalid_regex" model="formio.translation.source">
            <field name="property">invalid_regex</field>
            <field name="source">{{field}} does not match the pattern {{regex}}.</field>
        </record>
        <record id="i18n_invalid_date" model="formio.translation.source">
            <field name="property">invalid_date</field>
            <field name="source">{{field}} is not a valid date.</field>
        </record>
        <record id="i18n_invalid_day" model="formio.translation.source">
            <field name="property">invalid_day</field>
            <field name="source">{{field}} is not a valid day.</field>
        </record>
        <record id="i18n_invalid_mask" model="formio.translation.source">
            <field name="property">invalid_mask</field>
            <field name="source">{{field}} does not match the mask.</field>
        </record>
        <record id="i18n_invalid_mask" model="formio.translation.source">
            <field name="property">invalid_mask</field>
            <field name="source">{{field}} does not match the mask.</field>
        </record>
        <record id="i18n_stripe" model="formio.translation.source">
            <field name="property">stripe</field>
            <field name="source">{{stripe}}</field>
        </record>
        <record id="i18n_month" model="formio.translation.source">
            <field name="property">month</field>
            <field name="source">Month</field>
        </record>
        <record id="i18n_day" model="formio.translation.source">
            <field name="property">day</field>
            <field name="source">Day</field>
        </record>
        <record id="i18n_year" model="formio.translation.source">
            <field name="property">year</field>
            <field name="source">Year</field>
        </record>
        <record id="i18n_january" model="formio.translation.source">
            <field name="property">january</field>
            <field name="source">January</field>
        </record>
        <record id="i18n_february" model="formio.translation.source">
            <field name="property">february</field>
            <field name="source">February</field>
        </record>
        <record id="i18n_march" model="formio.translation.source">
            <field name="property">march</field>
            <field name="source">March</field>
        </record>
        <record id="i18n_april" model="formio.translation.source">
            <field name="property">april</field>
            <field name="source">April</field>
        </record>
        <record id="i18n_may" model="formio.translation.source">
            <field name="property">may</field>
            <field name="source">May</field>
        </record>
        <record id="i18n_june" model="formio.translation.source">
            <field name="property">june</field>
            <field name="source">June</field>
        </record>
        <record id="i18n_july" model="formio.translation.source">
            <field name="property">july</field>
            <field name="source">July</field>
        </record>
        <record id="i18n_august" model="formio.translation.source">
            <field name="property">august</field>
            <field name="source">August</field>
        </record>
        <record id="i18n_september" model="formio.translation.source">
            <field name="property">september</field>
            <field name="source">September</field>
        </record>
        <record id="i18n_october" model="formio.translation.source">
            <field name="property">october</field>
            <field name="source">October</field>
        </record>
        <record id="i18n_november" model="formio.translation.source">
            <field name="property">november</field>
            <field name="source">November</field>
        </record>
        <record id="i18n_december" model="formio.translation.source">
            <field name="property">december</field>
            <field name="source">December</field>
        </record>
        <record id="i18n_next" model="formio.translation.source">
            <field name="property">next</field>
            <field name="source">Next</field>
        </record>
        <record id="i18n_previous" model="formio.translation.source">
            <field name="property">previous</field>
            <field name="source">Previous</field>
        </record>
        <record id="i18n_cancel" model="formio.translation.source">
            <field name="property">cancel</field>
            <field name="source">Cancel</field>
        </record>
        <record id="i18n_submit" model="formio.translation.source">
            <field name="property">submit</field>
            <field name="source">Submit</field>
        </record>
        <record id="i18n_submitError" model="formio.translation.source">
            <field name="property">submitError</field>
            <field name="source">Please check the form and correct all errors before submitting.</field>
        </record>
    </data>
</odoo>
