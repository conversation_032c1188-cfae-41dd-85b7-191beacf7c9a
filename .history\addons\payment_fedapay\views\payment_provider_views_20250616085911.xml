<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">FedaPay Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <group name="provider_credentials" position='inside'>
                <group invisible="code != 'fedapay'">
                    <field name="fedapay_email_account" required="code == 'fedapay' and state != 'disabled'" />
                    <field name="fedapay_public_key" string="Client Public" password="True" required="code == 'fedapay' and state != 'disabled'" />
                    <field name="fedapay_client_secret" string="Client Secret" password="True" required="code == 'fedapay' and state != 'disabled'" />
                    <label for="fedapay_webhook_id" string="Webhook ID"/>
                    <div class="o_row" col="2">
                        <field name="fedapay_webhook_id"/>
                        <button string="Generate your webhook" type="object" name="action_fedapay_create_webhook" class="btn-primary" invisible="fedapay_webhook_id" />
                    </div>
                    <widget name="documentation_link" path="https://docs.fedapay.com/introduction/fr/keyconcepts-fr" label="How to configure your fedapay account?" colspan="2" />
                </group>
            </group>
        </field>
    </record>

</odoo>
