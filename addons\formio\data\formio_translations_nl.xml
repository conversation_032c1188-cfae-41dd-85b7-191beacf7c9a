<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <!-- nl -->
    <data noupdate="1">
        <record id="i18n_nl_complete" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_complete"/>
            <field name="value">Formulier is ingediend</field>
        </record>
        <record id="i18n_nl_error" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_error"/>
            <field name="value">Voordat u het formulier kunt opslaan, dient u de volgende fouten op te lossen.</field>
        </record>
        <record id="i18n_nl_alertMessage" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_alertMessage"/>
            <field name="value">{{message}}</field>
        </record>
        <record id="i18n_nl_required" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_required"/>
            <field name="value">{{field}} is verplicht</field>
        </record>
        <record id="i18n_nl_pattern" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_pattern"/>
            <field name="value">{{field}} voldoet niet aan het formaat {{pattern}}</field>
        </record>
        <record id="i18n_nl_minLength" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_minLength"/>
            <field name="value">{{field}} moet langer zijn dan {{length}} karakters.</field>
        </record>
        <record id="i18n_nl_maxLength" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_maxLength"/>
            <field name="value">{{field}} moet korter zijn dan {{length}} karakters.</field>
        </record>
        <record id="i18n_nl_min" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_min"/>
            <field name="value">{{field}} kan niet kleiner zijn dan {{min}}.</field>
        </record>
        <record id="i18n_nl_max" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_max"/>
            <field name="value">{{field}} kan niet groter zijn dan {{max}}.</field>
        </record>
        <record id="i18n_nl_maxDate" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_maxDate"/>
            <field name="value">{{field}} mag geen datum bevatten na {{- maxDate}}.</field>
        </record>
        <record id="i18n_nl_minDate" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_minDate"/>
            <field name="value">{{field}} mag geen datum bevatten voor {{- minDate}}.</field>
        </record>
        <record id="i18n_nl_invalid_email" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_invalid_email"/>
            <field name="value">{{field}} moet een geldig emailadres zijn.</field>
        </record>
        <record id="i18n_nl_invalid_url" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_invalid_url"/>
            <field name="value">{{field}} moet een geldige URL zijn.</field>
        </record>
        <record id="i18n_nl_invalid_regex" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_invalid_regex"/>
            <field name="value">{{field}} komt niet overeen met het patroon.</field>
        </record>
        <record id="i18n_nl_invalid_date" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_invalid_date"/>
            <field name="value">{{field}} is geen geldige datum.</field>
        </record>
        <record id="i18n_nl_invalid_day" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_invalid_day"/>
            <field name="value">{{field}} is geen geldige dag.</field>
        </record>
        <record id="i18n_nl_mask" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_invalid_mask"/>
            <field name="value">{{field}} komt niet overeen met het invoermasker.</field>
        </record>
        <record id="i18n_nl_stripe" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_stripe"/>
            <field name="value">{{stripe}}</field>
        </record>
        <record id="i18n_nl_month" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_month"/>
            <field name="value">Maand</field>
        </record>
        <record id="i18n_nl_day" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_day"/>
            <field name="value">Dag</field>
        </record>
        <record id="i18n_nl_year" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_year"/>
            <field name="value">Jaar</field>
        </record>
        <record id="i18n_nl_january" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_january"/>
            <field name="value">januari</field>
        </record>
        <record id="i18n_nl_february" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_february"/>
            <field name="value">februari</field>
        </record>
        <record id="i18n_nl_march" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_march"/>
            <field name="value">maart</field>
        </record>
        <record id="i18n_nl_april" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_april"/>
            <field name="value">april</field>
        </record>
        <record id="i18n_nl_may" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_may"/>
            <field name="value">mei</field>
        </record>
        <record id="i18n_nl_june" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_june"/>
            <field name="value">juni</field>
        </record>
        <record id="i18n_nl_july" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_july"/>
            <field name="value">juli</field>
        </record>
        <record id="i18n_nl_august" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_august"/>
            <field name="value">augustus</field>
        </record>
        <record id="i18n_nl_september" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_september"/>
            <field name="value">september</field>
        </record>
        <record id="i18n_nl_october" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_october"/>
            <field name="value">oktober</field>
        </record>
        <record id="i18n_nl_november" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_november"/>
            <field name="value">november</field>
        </record>
        <record id="i18n_nl_december" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_december"/>
            <field name="value">december</field>
        </record>
        <record id="i18n_nl_next" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_next"/>
            <field name="value">Volgende</field>
        </record>
        <record id="i18n_nl_previous" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_previous"/>
            <field name="value">Vorige</field>
        </record>
        <record id="i18n_nl_cancel" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_cancel"/>
            <field name="value">Annuleren</field>
        </record>
        <record id="i18n_nl_submit" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_submit"/>
            <field name="value">Verstuur formulier</field>
        </record>
        <record id="i18n_nl_submitError" model="formio.translation">
            <field name="lang_id" ref="base.lang_nl"/>
            <field name="source_id" ref="formio.i18n_submitError"/>
            <field name="value">Controleer het formulier en corrigeer alle fouten voordat u het verzendt.</field>
        </record>
    </data>
</odoo>
