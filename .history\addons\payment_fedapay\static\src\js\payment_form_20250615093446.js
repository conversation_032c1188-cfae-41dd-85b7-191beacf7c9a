/* global paypal */

import { loadJS } from '@web/core/assets';
import { _t } from '@web/core/l10n/translation';
import { rpc, RPCError } from '@web/core/network/rpc';

import paymentForm from '@payment/js/payment_form';

paymentForm.include({
    inlineFormValues: undefined,
    paypalColor: 'blue',
    selectedOptionId: undefined,
    paypalData: undefined,


    // #=== DOM MANIPULATION ===#

    /**
     * Hides fedapay button container if the expanded inline form is another provider.
     *
     * @private
     * @param {HTMLInputElement} radio - The radio button linked to the payment option.
     * @return {void}
     */
    async _expandInlineForm(radio) {
        const providerCode = this._getProviderCode(radio);
        this._super(...arguments);
    },

    /**
     * Prepare the inline form of FedaPay for direct payment.
     *
     * The FedaPay SDK creates payment buttons based on the client_id and the currency of the order.
     *
     * Two payment buttons are created: one enabled and one disabled. The enabled button is shown
     * when the user is allowed to click on it, and the disabled button is shown otherwise. This
     * trick is necessary as the FedaPay SDK does not provide a way to disable the button after it
     * has been created.
     *
     * The created buttons are saved and reused when switching between different payment methods to
     * avoid recreating the buttons.
     *
     * @override method from @payment/js/payment_form
     * @private
     * @param {number} providerId - The id of the selected payment option's provider.
     * @param {string} providerCode - The code of the selected payment option's provider.
     * @param {number} paymentOptionId - The id of the selected payment option
     * @param {string} paymentMethodCode - The code of the selected payment method, if any.
     * @param {string} flow - The online payment flow of the selected payment option.
     * @return {void}
     */
    async _prepareInlineForm(providerId, providerCode, paymentOptionId, paymentMethodCode, flow) {
        console.log("Prepare inline form", providerId, providerCode, paymentOptionId, paymentMethodCode, flow);
        
        
        
        if (providerCode !== 'fedapay') {
            this._super(...arguments);
            return;
        }

        this._hideInputs();
        this._setPaymentFlow('direct');
        document.getElementById('o_fedapay_loading').classList.remove('d-none');
        // Check if instantiation of the component is needed.
        this.fedapayData ??= {}; // Store the component of each instantiated payment method.
        const currentPayPalData = this.fedapayData[paymentOptionId]
        console.log("currentPayPalData", currentPayPalData);
        if (!currentPayPalData) {
            console.log("!currentPayPalData");
            
            this.fedapayData[paymentOptionId] = {}
            const radio = document.querySelector('input[name="o_payment_radio"]:checked');
            if (radio) {
                console.log("radio", radio);
                this.inlineFormValues = JSON.parse(radio.dataset['fedapayInlineFormValues']);
                this.fedapayColor = radio.dataset['fedapayColor']
            }

            // https://cdn.fedapay.com/checkout.js?v=1.1.7
            const { client_id, currency_code, fedapay_public_key } = this.inlineFormValues

            console.log("inlineFormValues", this.inlineFormValues);
            console.log("paypalData", this.fedapayData);            
            const paypalSDKURL = `https://cdn.fedapay.com/checkout.js?v=1.1.7`
            this.fedapayData[paymentOptionId]['sdkURL'] = paypalSDKURL;
            await loadJS(paypalSDKURL);
            const enabledButtonContainer = document.getElementById('o_fedapay_button_container');
            console.log("enabledButtonContainer update", enabledButtonContainer);
            // Création du bouton "Payer"
            const payButton = document.createElement('button');
            payButton.name = 'o_fedapay_payment_button';
            payButton.id = 'pay-btn';
            payButton.className = 'btn btn-primary w-100 w-md-auto ms-auto px-5';
            payButton.innerText = 'Feda';

            // Ajout du bouton dans le conteneur
            enabledButtonContainer.innerHTML = '';  // Nettoie le conteneur avant d’ajouter le bouton
            enabledButtonContainer.appendChild(payButton);
            // ➕ Ajoute un événement de clic pour ouvrir une nouvelle fenêtre
            payButton.addEventListener('click', function (e) {
                e.preventDefault();

                // Dimensions de la popup (similaires à PayPal)
                const width = 520;
                const height = 480;

                // Calcul du centrage
                const left = (window.screen.width / 2) - (width / 2);
                const top = (window.screen.height / 2) - (height / 2);

                // Ouvrir la fenêtre centrée
                const win = window.open('', 'FedaPayPopup', `width=${width},height=${height},left=${left},top=${top}`);

                if (!win) {
                    alert("Veuillez autoriser les popups pour continuer le paiement.");
                    return;
                }

                // Contenu HTML de la popup avec FedaPay intégré
                const html = `
                    <!doctype html>
                    <html lang="fr">
                    <head>
                        <meta charset="utf-8">
                        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
                        <title>Paiement FedaPay</title>
                        <script src="https://cdn.fedapay.com/checkout.js?v=1.1.7"></script>
                        <style>
                            html, body {
                                margin: 0;
                                padding: 0;
                                height: 100%;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                background-color: #f8f9fa;
                                font-family: Arial, sans-serif;
                            }
                            #embed {
                                width: 500px;
                                height: 420px;
                                background-color: white;
                            }
                        </style>
                    </head>
                    <body>
                        <div id="embed"></div>
                        <script type="text/javascript">
                            FedaPay.init({
                                public_key: '${fedapay_public_key}',
                                transaction: {
                                    amount: 10000,
                                    description: 'Achat via Odoo'
                                },
                                customer: {
                                    email: '<EMAIL>',
                                    lastname: 'Kavege',
                                    firstname: 'Julio'
                                },
                                container: '#embed',
                                onComplete: function (reason, transaction) {
                                    console.log("onComplete in html", reason, transaction);
                                    window.opener.postMessage({
                                        type: 'fedapay_complete',
                                        reason: reason,
                                        transaction: transaction
                                    }, '*');
                                    window.close();
                                }
                            });
                        </script>
                    </body>
                    </html>
                `;

                // Injecter le HTML
                win.document.open();
                win.document.write(html);
                win.document.close();
            });
        }
        document.getElementById('o_fedapay_loading').classList.add('d-none');
        document.getElementById('o_fedapay_button_container')?.classList.remove('d-none');  // TODO Compatibility layer; remove the ? in master.
        this.selectedOptionId = paymentOptionId;
        
    }  
});

window.addEventListener('message', (event) => {
    if (event.data.type === 'fedapay_complete') {
        fedaPayOnComplete(event.data.reason, event.data.transaction);
    }
});

// #=== PAYMENT FLOW ===#

function fedaPayOnComplete(reason, transaction) {
    console.log("_fedaPayOnComplete", reason, transaction);
}
