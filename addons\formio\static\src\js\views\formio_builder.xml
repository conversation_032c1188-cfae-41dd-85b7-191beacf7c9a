<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="formio.BuilderView" owl="1">
        <t t-set="builder" t-value="model.root.data"/>
        <div class="o_action h-100 o_form_view">
            <div class="o_form_view_container" t-ref="root">
                <Layout display="display">
                    <div class="o_form_editable d-flex flex-nowrap h-100">
                        <div class="o_form_sheet_bg">
                            <div id="formio_builder_container" t-attf-class="clearfix o_form_sheet {{ builder.state }} ">
                                <h1><span name="title" t-out="builder.title"/></h1>
                                <div class="formio_builder_dock">
                                    <ul>
                                        <li class="fullscreen_formio btn-group">
                                            <button id="fullscreen_formio" class="btn btn-sm btn-default pull-right">Fullscreen (Exit with ESC)</button>
                                        </li>
                                        <li class="version">
                                            <span class="formio_dock_label">Version</span><span t-out="builder.version"/>
                                        </li>
                                        <t t-if="builder.parent_version">
                                            <li class="parent">
                                                <span class="formio_dock_label">Parent version</span><span t-out="builder.parent_version"/>
                                            </li>
                                        </t>
                                        <li class="formio_version">
                                            <span class="formio_dock_label">formio.js version</span><span t-out="builder.formio_version_name"/>
                                        </li>
                                        <li t-if="builder.is_locked == false" class="auto_save">
                                            <t t-if="builder.auto_save">
                                                <span class="badge rounded-pill text-bg-warning mb-1">
                                                    <span class="formio_dock_label">Auto-save</span>
                                                    <span>Enabled</span>
                                                </span>
                                            </t>
                                            <t t-else="">
                                                <span class="badge rounded-pill text-bg-info mb-1">
                                                    <span class="formio_dock_label">Auto-save</span>
                                                    <span class="formio_dock_val">Disabled</span>
                                                </span>
                                            </t>
                                        </li>
                                        <li t-if="builder.state == 'CURRENT'" class="locked">
                                            <t t-if="builder.is_locked">
                                                <span class="badge rounded-pill text-bg-info mb-1">
                                                    <span class="formio_dock_label">Locked</span>
                                                </span>
                                            </t>
                                            <t t-else="">
                                                <span class="badge rounded-pill text-bg-warning mb-1">
                                                    <span class="formio_dock_label">Unlocked</span>
                                                </span>
                                            </t>
                                        </li>
                                        <li class="builder_state">
                                            <t t-if="builder.state == 'DRAFT'">
                                                <span t-attf-class="badge rounded-pill text-bg-warning mb-1">
                                                    <span class="formio_dock_label">State</span>
                                                    <span class="formio_dock_val" t-out="builder.display_state"/>
                                                </span>
                                            </t>
                                            <t t-elif="builder.state == 'CURRENT'">
                                                <span t-attf-class="badge rounded-pill text-bg-success mb-1">
                                                    <span class="formio_dock_label">State</span>
                                                    <span class="formio_dock_val" t-out="builder.display_state"/>
                                                </span>
                                            </t>
                                            <t t-elif="builder.state == 'OBSOLETE'">
                                                <span t-attf-class="badge rounded-pill text-bg-dark mb-1">
                                                    <span class="formio_dock_label">State</span>
                                                    <span class="formio_dock_val" t-out="builder.display_state"/>
                                                </span>
                                            </t>
                                            <t t-else="">
                                                <span t-attf-class="badge rounded-pill text-bg-light mb-1">
                                                    <span class="formio_dock_label">State</span>
                                                    <span class="formio_dock_val" t-out="builder.display_state"/>
                                                </span>
                                            </t>
                                        </li>
                                    </ul>
                                </div>
                                <div id="formio_builder_iframe_container">
                                    <iframe t-attf-src="/formio/builder/{{ builder.id }}" id="formio_builder_embed" allowfullscreen="true"/>
                                    <script>
                                        <!-- use var here, due to redeclaration error with const and let -->
                                        var fullscreen = document.getElementById('fullscreen_formio');
                                        var iframe = document.getElementById('formio_builder_embed');
                                        fullscreen.addEventListener('click', (function () {
                                            iframe.requestFullscreen();
                                        }));
                                    </script>
                                </div>
                            </div>
                        </div>
                    </div>
                </Layout>
            </div>
        </div>
    </t>

</templates>
