version: '2'
services:
  db:
    image: postgres:17
    user: root
    environment:
      - POSTGRES_USER=odoo
      - POSTGRES_PASSWORD=odoo18@2024
      - POSTGRES_DB=postgres
    restart: always             # run as a service
    volumes:
        - ./postgresql:/var/lib/postgresql/data

  odoo18:
    image: odoo:18
    user: root
    depends_on:
      - db
    ports:
      - "18000:8069"
      - "28000:8072" # live chat
    tty: true
    command: --
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo18@2024
      - PIP_BREAK_SYSTEM_PACKAGES=1
    volumes:
      #- /etc/timezone:/etc/timezone:ro
      #- /etc/localtime:/etc/localtime:ro
      # - ./entrypoint.sh:/entrypoint.sh
      - ./addons:/mnt/extra-addons
      - ./etc:/etc/odoo
    restart: always             # run as a service
    