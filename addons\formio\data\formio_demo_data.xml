<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <data noupdate="1">
        <record id="formio_builder_demo" model="formio.builder">
            <field name="name">demo_1</field>
            <field name="title">Demo 1</field>
            <field name="formio_version_id" ref="formio.version_dummy"/>
            <field name="schema">{"components": [{"label": "Columns", "columns": [{"components": [{"label": "Firstname", "tableView": true, "key": "firstname", "type": "textfield", "input": true, "reorder": false, "placeholder": "", "prefix": "", "customClass": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": true, "hidden": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "modalEdit": false, "dataGridLabel": false, "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": {"type": "input"}, "attributes": {}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false, "minLength": "", "maxLength": "", "pattern": ""}, "conditional": {"show": null, "when": null, "eq": ""}, "overlay": {"style": "", "left": "", "top": "", "width": "", "height": ""}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "properties": {}, "allowMultipleMasks": false, "addons": [], "mask": false, "inputType": "text", "inputFormat": "plain", "inputMask": "", "displayMask": "", "spellcheck": true, "truncateMultipleSpaces": false, "id": "eyo2qup"}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}, {"components": [{"label": "Lastname", "tableView": true, "key": "lastname", "type": "textfield", "input": true, "placeholder": "", "prefix": "", "customClass": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": true, "hidden": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "modalEdit": false, "dataGridLabel": false, "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": {"type": "input"}, "attributes": {}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false, "minLength": "", "maxLength": "", "pattern": ""}, "conditional": {"show": null, "when": null, "eq": ""}, "overlay": {"style": "", "left": "", "top": "", "width": "", "height": ""}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "properties": {}, "allowMultipleMasks": false, "addons": [], "mask": false, "inputType": "text", "inputFormat": "plain", "inputMask": "", "displayMask": "", "spellcheck": true, "truncateMultipleSpaces": false, "id": "e7sk6cs"}], "width": 6, "offset": 0, "push": 0, "pull": 0, "size": "md", "currentWidth": 6}, {"components": [{"label": "Email", "labelPosition": "top", "placeholder": "", "description": "", "tooltip": "", "prefix": "", "suffix": "", "widget": {"type": "input"}, "displayMask": "", "customClass": "", "tabindex": "", "autocomplete": "", "hidden": false, "hideLabel": false, "mask": false, "autofocus": false, "spellcheck": true, "disabled": false, "tableView": true, "modalEdit": false, "multiple": false, "persistent": true, "inputFormat": "plain", "protected": false, "dbIndex": false, "case": "", "truncateMultipleSpaces": false, "encrypted": false, "redrawOn": "", "clearOnHide": true, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "allowCalculateOverride": false, "validateOn": "change", "validate": {"required": true, "pattern": "", "customMessage": "", "custom": "", "customPrivate": false, "json": "", "minLength": "", "maxLength": "", "strictDateValidation": false, "multiple": false, "unique": false}, "unique": false, "kickbox": {"enabled": false}, "errorLabel": "", "errors": "", "key": "email", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "email", "input": true, "refreshOn": "", "dataGridLabel": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "inputType": "email", "inputMask": "", "id": "ey4l6ey", "defaultValue": ""}], "size": "md", "width": 6, "offset": 0, "push": 0, "pull": 0, "currentWidth": 6}], "autoAdjust": false, "customClass": "", "hidden": false, "hideLabel": false, "modalEdit": false, "key": "columns", "tags": [], "properties": {}, "conditional": {"show": null, "when": null, "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "columns", "input": false, "tableView": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "clearOnHide": false, "refreshOn": "", "redrawOn": "", "dataGridLabel": false, "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "tabindex": "", "disabled": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": null, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "addons": [], "tree": false, "lazyLoad": false, "id": "ei6ygrr"}, {"label": "Columns", "columns": [{"components": [{"label": "Save as Draft", "action": "saveState", "showValidations": false, "theme": "secondary", "size": "md", "block": false, "leftIcon": "", "rightIcon": "", "shortcut": "", "description": "", "tooltip": "", "customClass": "", "tabindex": "", "disableOnInvalid": false, "hidden": false, "autofocus": false, "disabled": false, "alwaysEnabled": false, "tableView": true, "key": "saveDraft", "tags": "", "properties": {"submit": "false"}, "conditional": {"show": "", "when": "", "eq": "", "json": ""}, "customConditional": "", "logic": [], "attributes": {}, "overlay": {"style": "", "page": "", "left": "", "top": "", "width": "", "height": ""}, "type": "button", "mask": false, "input": true, "defaultValue": false, "validate": {"customMessage": "", "json": "", "required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "state": "draft", "event": "", "url": "", "custom": "", "reorder": false, "encrypted": false, "hideOnChildrenHidden": false, "placeholder": "", "prefix": "", "suffix": "", "multiple": false, "protected": false, "unique": false, "persistent": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "labelPosition": "top", "errorLabel": "", "hideLabel": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "widget": {"type": "input"}, "validateOn": "change", "allowCalculateOverride": false, "showCharCount": false, "showWordCount": false, "allowMultipleMasks": false, "dataGridLabel": true, "id": "eqg655l", "modalEdit": false, "calculateServer": false, "addons": []}], "width": 3, "offset": 0, "push": 0, "pull": 0, "type": "column", "input": false, "hideOnChildrenHidden": false, "key": "column", "tableView": true, "label": "Column", "size": "md", "currentWidth": 3}, {"components": [{"type": "button", "label": "Submit", "key": "submit", "disableOnInvalid": true, "theme": "primary", "input": true, "tableView": true, "hideOnChildrenHidden": false, "placeholder": "", "prefix": "", "customClass": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "hidden": false, "clearOnHide": true, "refreshOn": "", "redrawOn": "", "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "widget": {"type": "input"}, "attributes": {}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "conditional": {"show": null, "when": null, "eq": ""}, "overlay": {"style": "", "left": "", "top": "", "width": "", "height": ""}, "allowCalculateOverride": false, "encrypted": false, "alwaysEnabled": false, "showCharCount": false, "showWordCount": false, "properties": {}, "allowMultipleMasks": false, "size": "md", "leftIcon": "", "rightIcon": "", "block": false, "action": "submit", "dataGridLabel": true, "id": "e6i634", "modalEdit": false, "calculateServer": false, "addons": []}], "width": 2, "offset": 0, "push": 0, "pull": 0, "type": "column", "input": false, "hideOnChildrenHidden": false, "key": "column", "tableView": true, "label": "Column", "size": "md", "currentWidth": 2}], "mask": false, "type": "columns", "input": false, "key": "columns2", "reorder": false, "placeholder": "", "prefix": "", "customClass": "", "suffix": "", "multiple": false, "defaultValue": null, "protected": false, "unique": false, "persistent": false, "hidden": false, "clearOnHide": false, "refreshOn": "", "redrawOn": "", "tableView": false, "modalEdit": false, "dataGridLabel": false, "labelPosition": "top", "description": "", "errorLabel": "", "tooltip": "", "hideLabel": false, "tabindex": "", "disabled": false, "autofocus": false, "dbIndex": false, "customDefaultValue": "", "calculateValue": "", "calculateServer": false, "widget": null, "attributes": {}, "validateOn": "change", "validate": {"required": false, "custom": "", "customPrivate": false, "strictDateValidation": false, "multiple": false, "unique": false}, "conditional": {"show": null, "when": null, "eq": ""}, "overlay": {"style": "", "left": "", "top": "", "width": "", "height": ""}, "allowCalculateOverride": false, "encrypted": false, "showCharCount": false, "showWordCount": false, "properties": {}, "allowMultipleMasks": false, "addons": [], "tree": false, "lazyLoad": false, "autoAdjust": false, "id": "etxsdtl"}]}</field>
        </record>
    </data>
</odoo>
