/*! noble-ed25519 - MIT License (c) 2019 <PERSON> (paulmillr.com) */
const P=2n**255n-19n,N=2n**252n+27742317777372353535851937790883648493n,Gx=0x216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51an,Gy=0x6666666666666666666666666666666666666666666666666666666666666658n,CURVE={a:-1n,d:37095705934669439343138083508754565189542113879843219016388785533085940283555n,p:P,n:N,h:8,Gx,Gy},err=(e="")=>{throw new Error(e)},str=e=>"string"==typeof e,au8=(e,t)=>!(e instanceof Uint8Array)||"number"==typeof t&&t>0&&e.length!==t?err("Uint8Array expected"):e,u8n=e=>new Uint8Array(e),toU8=(e,t)=>au8(str(e)?h2b(e):u8n(e),t),mod=(e,t=P)=>{let n=e%t;return n>=0n?n:t+n},isPoint=e=>e instanceof Point?e:err("Point expected");let Gpows;class Point{constructor(e,t,n,o){this.ex=e,this.ey=t,this.ez=n,this.et=o}static fromAffine(e){return new Point(e.x,e.y,1n,mod(e.x*e.y))}static fromHex(e,t=!0){const{d:n}=CURVE,o=(e=toU8(e,32)).slice();o[31]=-129&e[31];const s=b2n_LE(o);0n===s||(!t||0n<s&&s<P||err("bad y coord 1"),t||0n<s&&s<2n**256n||err("bad y coord 2"));const r=mod(s*s),i=mod(r-1n),a=mod(n*r+1n);let{isValid:d,value:c}=uvRatio(i,a);d||err("bad y coordinate 3");const h=1n===(1n&c);return 0!=(128&e[31])!==h&&(c=mod(-c)),new Point(c,s,1n,mod(c*s))}get x(){return this.toAffine().x}get y(){return this.toAffine().y}equals(e){const{ex:t,ey:n,ez:o}=this,{ex:s,ey:r,ez:i}=isPoint(e),a=mod(t*i),d=mod(s*o),c=mod(n*i),h=mod(r*o);return a===d&&c===h}is0(){return this.equals(I)}negate(){return new Point(mod(-this.ex),this.ey,this.ez,mod(-this.et))}double(){const{ex:e,ey:t,ez:n}=this,{a:o}=CURVE,s=mod(e*e),r=mod(t*t),i=mod(2n*mod(n*n)),a=mod(o*s),d=e+t,c=mod(mod(d*d)-s-r),h=a+r,u=h-i,l=a-r,y=mod(c*u),m=mod(h*l),f=mod(c*l),b=mod(u*h);return new Point(y,m,b,f)}add(e){const{ex:t,ey:n,ez:o,et:s}=this,{ex:r,ey:i,ez:a,et:d}=isPoint(e),{a:c,d:h}=CURVE,u=mod(t*r),l=mod(n*i),y=mod(s*h*d),m=mod(o*a),f=mod((t+n)*(r+i)-u-l),b=mod(m-y),p=mod(m+y),P=mod(l-c*u),x=mod(f*b),g=mod(p*P),w=mod(f*P),_=mod(b*p);return new Point(x,g,_,w)}mul(e,t=!0){if(0n===e)return!0===t?err("cannot multiply by 0"):I;if("bigint"==typeof e&&0n<e&&e<N||err("invalid scalar, must be < L"),!t&&this.is0()||1n===e)return this;if(this.equals(G))return wNAF(e).p;let n=I,o=G;for(let s=this;e>0n;s=s.double(),e>>=1n)1n&e?n=n.add(s):t&&(o=o.add(s));return n}multiply(e){return this.mul(e)}clearCofactor(){return this.mul(BigInt(CURVE.h),!1)}isSmallOrder(){return this.clearCofactor().is0()}isTorsionFree(){let e=this.mul(N/2n,!1).double();return N%2n&&(e=e.add(this)),e.is0()}toAffine(){const{ex:e,ey:t,ez:n}=this;if(this.is0())return{x:0n,y:0n};const o=invert(n);return 1n!==mod(n*o)&&err("invalid inverse"),{x:mod(e*o),y:mod(t*o)}}toRawBytes(){const{x:e,y:t}=this.toAffine(),n=n2b_32LE(t);return n[31]|=1n&e?128:0,n}toHex(){return b2h(this.toRawBytes())}}Point.BASE=new Point(Gx,Gy,1n,mod(Gx*Gy)),Point.ZERO=new Point(0n,1n,1n,0n);const{BASE:G,ZERO:I}=Point,padh=(e,t)=>e.toString(16).padStart(t,"0"),b2h=e=>Array.from(e).map((e=>padh(e,2))).join(""),h2b=e=>{const t=e.length;(!str(e)||t%2)&&err("hex invalid 1");const n=u8n(t/2);for(let t=0;t<n.length;t++){const o=2*t,s=e.slice(o,o+2),r=Number.parseInt(s,16);(Number.isNaN(r)||r<0)&&err("hex invalid 2"),n[t]=r}return n},n2b_32LE=e=>h2b(padh(e,64)).reverse(),b2n_LE=e=>BigInt("0x"+b2h(u8n(au8(e)).reverse())),concatB=(...e)=>{const t=u8n(e.reduce(((e,t)=>e+au8(t).length),0));let n=0;return e.forEach((e=>{t.set(e,n),n+=e.length})),t},invert=(e,t=P)=>{(0n===e||t<=0n)&&err("no inverse n="+e+" mod="+t);let n=mod(e,t),o=t,s=0n,r=1n,i=1n,a=0n;for(;0n!==n;){const e=o/n,t=o%n,d=s-i*e,c=r-a*e;o=n,n=t,s=i,r=a,i=d,a=c}return 1n===o?mod(s,t):err("no inverse")},pow2=(e,t)=>{let n=e;for(;t-- >0n;)n*=n,n%=P;return n},pow_2_252_3=e=>{const t=e*e%P*e%P,n=pow2(t,2n)*t%P,o=pow2(n,1n)*e%P,s=pow2(o,5n)*o%P,r=pow2(s,10n)*s%P,i=pow2(r,20n)*r%P,a=pow2(i,40n)*i%P,d=pow2(a,80n)*a%P,c=pow2(d,80n)*a%P,h=pow2(c,10n)*s%P;return{pow_p_5_8:pow2(h,2n)*e%P,b2:t}},RM1=19681161376707505956807079304988542015446066515923890162744021073123829784752n,uvRatio=(e,t)=>{const n=mod(t*t*t),o=mod(n*n*t),s=pow_2_252_3(e*o).pow_p_5_8;let r=mod(e*n*s);const i=mod(t*r*r),a=r,d=mod(r*RM1),c=i===e,h=i===mod(-e),u=i===mod(-e*RM1);return c&&(r=a),(h||u)&&(r=d),1n===(1n&mod(r))&&(r=mod(-r)),{isValid:c||h,value:r}},modL_LE=e=>mod(b2n_LE(e),N);let _shaS;const sha512a=(...e)=>etc.sha512Async(...e),sha512s=(...e)=>"function"==typeof _shaS?_shaS(...e):err("etc.sha512Sync not set"),hash2extK=e=>{const t=e.slice(0,32);t[0]&=248,t[31]&=127,t[31]|=64;const n=e.slice(32,64),o=modL_LE(t),s=G.mul(o),r=s.toRawBytes();return{head:t,prefix:n,scalar:o,point:s,pointBytes:r}},getExtendedPublicKeyAsync=e=>sha512a(toU8(e,32)).then(hash2extK),getExtendedPublicKey=e=>hash2extK(sha512s(toU8(e,32))),getPublicKeyAsync=e=>getExtendedPublicKeyAsync(e).then((e=>e.pointBytes)),getPublicKey=e=>getExtendedPublicKey(e).pointBytes;function hashFinish(e,t){return e?sha512a(t.hashable).then(t.finish):t.finish(sha512s(t.hashable))}const _sign=(e,t,n)=>{const{pointBytes:o,scalar:s}=e,r=modL_LE(t),i=G.mul(r).toRawBytes();return{hashable:concatB(i,o,n),finish:e=>{const t=mod(r+modL_LE(e)*s,N);return au8(concatB(i,n2b_32LE(t)),64)}}},signAsync=async(e,t)=>{const n=toU8(e),o=await getExtendedPublicKeyAsync(t),s=await sha512a(o.prefix,n);return hashFinish(!0,_sign(o,s,n))},sign=(e,t)=>{const n=toU8(e),o=getExtendedPublicKey(t),s=sha512s(o.prefix,n);return hashFinish(!1,_sign(o,s,n))},_verify=(e,t,n)=>{t=toU8(t),e=toU8(e,64);const o=Point.fromHex(n,!1),s=Point.fromHex(e.slice(0,32),!1),r=b2n_LE(e.slice(32,64)),i=G.mul(r,!1);return{hashable:concatB(s.toRawBytes(),o.toRawBytes(),t),finish:e=>{const t=modL_LE(e);return s.add(o.mul(t,!1)).add(i.negate()).clearCofactor().is0()}}},verifyAsync=async(e,t,n)=>hashFinish(!0,_verify(e,t,n)),verify=(e,t,n)=>hashFinish(!1,_verify(e,t,n)),cr=()=>"object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0,etc={bytesToHex:b2h,hexToBytes:h2b,concatBytes:concatB,mod,invert,randomBytes:e=>{const t=cr();return t||err("crypto.getRandomValues must be defined"),t.getRandomValues(u8n(e))},sha512Async:async(...e)=>{const t=cr();t||err("crypto.subtle or etc.sha512Async must be defined");const n=concatB(...e);return u8n(await t.subtle.digest("SHA-512",n.buffer))},sha512Sync:void 0};Object.defineProperties(etc,{sha512Sync:{configurable:!1,get:()=>_shaS,set(e){_shaS||(_shaS=e)}}});const utils={getExtendedPublicKeyAsync,getExtendedPublicKey,randomPrivateKey:()=>etc.randomBytes(32),precompute:(e=8,t=G)=>(t.multiply(3n),t)},W=8,precompute=()=>{const e=[];let t=G,n=t;for(let o=0;o<33;o++){n=t,e.push(n);for(let o=1;o<128;o++)n=n.add(t),e.push(n);t=n.double()}return e},wNAF=e=>{const t=Gpows||(Gpows=precompute()),n=(e,t)=>{let n=t.negate();return e?n:t};let o=I,s=G;const r=BigInt(255),i=BigInt(8);for(let a=0;a<33;a++){const d=128*a;let c=Number(e&r);e>>=i,c>128&&(c-=256,e+=1n);const h=d,u=d+Math.abs(c)-1,l=a%2!=0,y=c<0;0===c?s=s.add(n(l,t[h])):o=o.add(n(y,t[u]))}return{p:o,f:s}};export{getPublicKey,getPublicKeyAsync,sign,verify,signAsync,verifyAsync,CURVE,etc,utils,Point as ExtendedPoint};
