<section class="oe_container">
  <div class="oe_row oe_spaced">
    <h2 class="oe_slogan" style="color:#875A7B;"><b>Forms &bull; Data API</b></h2>
    <h3 class="oe_slogan mt8">Python API for Form Builder and Form/Submission data</h3>
    <div class="oe_row">
      <h5>Installation Requirements</h5>
      <ul>
        <li>
          PyPi package <strong><code>formio-data</code> (version >= 0.4.10)</strong><br/>
          - Install: <code>pip3 install -U formio-data</code><br/>
          - GitHub project: <code>https://github.com/novacode-nl/python-formio-data</code>
        </li>
      </ul>
    </div>
  </div>
</section>

<section class="oe_container">
  <div class="oe_row oe_centeralign mb32">
    <h2 class="oe_slogan" style="color:#875A7B;">User Documentation</h2>
    <h5>
      <i class="fa fa-github"/> User Documentation is available on the GitHub Wiki<br/><br/>
      <code>https://github.com/novacode-nl/odoo-formio/wiki</code>
    </h5>
  </div>
  <div class="oe_row oe_centeralign mb16">
    <h2 class="oe_slogan" style="color:#875A7B;">Visit the online Demo</h2>
    <p>Click the <strong>Live Preview</strong> button in the top.</p>
    <p class="mt8"><strong>Users</strong> (email / password)</p>
    <ul>
      <li>demo / demo</li>
      <li>portal / portal</li>
    </ul>
  </div>
</section>
