# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* formio
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-07 19:07+0000\n"
"PO-Revision-Date: 2024-04-07 21:13+0200\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__state
msgid ""
"        - Available: Not downloaded and installed yet.\n"
"        - Installed: Downloaded and installed."
msgstr ""
"        - Beschikbaar: Nog niet gedownload en geïnstalleerd.\n"
"        - Geïnstalleerd: Gedownloaded en geïnstalleerd."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__state
msgid ""
"        - Draft: In draft / design.\n"
"        - Current: Live and in use (published).\n"
"        - Obsolete: Was current but obsolete (unpublished)"
msgstr ""
"        - Concept: In concept / bewerking.\n"
"        - Actueel: Live en in gebruik (gepubliceerd).\n"
"        - Verouderd: Niet live, was eerder actueel (niet gepubliceerd)"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__is_locked
msgid ""
"        - Locked: No further modifications are possible in the Form Builder "
"and configuration.\n"
"        - Unlocked: Modications are possible, but could cause existing forms "
"to be invalid."
msgstr ""
"- Vergrendeld: er zijn geen verdere wijzigingen mogelijk in de Form Builder "
"en configuratie.\n"
"- Ontgrendeld: wijzigingen zijn mogelijk, maar kunnen bestaande formulieren "
"veroorzaken ongeldig zijn."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__portal_save_draft_done_url
#: model:ir.model.fields,help:formio.field_formio_builder__portal_submit_done_url
#: model:ir.model.fields,help:formio.field_formio_builder__public_save_draft_done_url
#: model:ir.model.fields,help:formio.field_formio_builder__public_submit_done_url
#: model:ir.model.fields,help:formio.field_formio_form__portal_save_draft_done_url
#: model:ir.model.fields,help:formio.field_formio_form__portal_submit_done_url
#: model:ir.model.fields,help:formio.field_formio_form__public_save_draft_done_url
#: model:ir.model.fields,help:formio.field_formio_form__public_submit_done_url
msgid ""
"        IMPORTANT:\n"
"        - Absolute URL should contain a protocol (https://, http://)\n"
"        - Relative URL is also supported e.g. /web/login\n"
"        "
msgstr ""
"        BELANGRIJK:\n"
"        - Absolute URL moet een protocol bevatten (https://, http://)\n"
"        - Relatieve URL is ook ondersteunt bijv. /web/login"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__iframe_resizer_body_margin
#: model:ir.model.fields,help:formio.field_formio_form__iframe_resizer_body_margin
msgid ""
"        Override the default body margin style in the iFrame.\n"
"        A string can be any valid value for the CSS margin property.\n"
"        A number is converted into px.\n"
"        Example: 0px 0px 260px 0px\n"
"        "
msgstr ""
"Override de standaard body-margestijl in het iFrame.\n"
"Een tekenreeks kan elke geldige waarde zijn voor de CSS margin property.\n"
"Een getal wordt omgezet in px.\n"
"Voorbeeld: 0px 0px 260px 0px"

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_builder_translation.py:0
#, python-format
msgid ""
"- Form Builder Name: {name}\n"
"- Source Term: {source}\n"
"- Translation: {translation}"
msgstr ""
"- Form Builder Naam: {name}\n"
"- Bronterm: {source}\n"
"- Vertaling: {translation}"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"<br/><br/>\n"
"                                Examples:"
msgstr ""

#. module: formio
#: model:mail.template,body_html:formio.mail_invitation_internal_user
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.user_id.partner_id.name\"></t>,<br><br>\n"
"        You have been invited to fill-in the form: <t t-out=\"object."
"title\"></t><br>\n"
"        Your response would be appreciated.<br><br>\n"
"        Click the button to go to the form (record), which requires you're "
"logged in.\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-attf-href=\"{{ object.act_window_url }}\" "
"style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-"
"decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                Form\n"
"            </a>\n"
"        </div>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Beste <t t-out=\"object.user_id.partner_id.name\">,<br/><br/>\n"
"        U bent uitgenodigd voor het invullen van het formulier: <t t-"
"out=\"object.title/><br/>\n"
"        Klik op de knop om het formulier te openen. Daarvoor moet u zijn "
"ingelogd.\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a href=\"<t t-out=\"object.act_window_url\"/>\n"
"                style=\"background-color: #875A7B; padding: 8px 16px 8px "
"16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;"
"\">\n"
"                Formulier\n"
"            </a>\n"
"        </div>\n"
"    </p>\n"
"</div>"

#. module: formio
#: model:mail.template,body_html:formio.mail_invitation_portal_user
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.user_id.partner_id.name\"></t>,<br><br>\n"
"        You have been invited to fill-in the form: <t t-out=\"object."
"title\"></t><br>\n"
"        Your response would be appreciated.<br><br>\n"
"        Click the button to go to the form, which requires you're logged "
"in.\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-attf-href=\"/my/formio/form/{{ object.uuid }}\" "
"style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-"
"decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                Form\n"
"            </a>\n"
"        </div>\n"
"        Your assigned forms are listed on the page <a href=\"/my/formio\">My "
"Forms</a> \n"
"    </p>\n"
"</div>\n"

msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Beste <t t-out=\"object.user_id.partner_id.name/>,<br/><br/>\n"
"        U bent uitgenodigd voor het invullen van het formulier: <t t-"
"out=\"object.title\"/><br/>\n"
"        Klik op de knop om het formulier te openen. Daarvoor moet u zijn "
"ingelogd.\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a href=\"<t t-out=\"object.url\"/>\"\n"
"                style=\"background-color: #875A7B; padding: 8px 16px 8px "
"16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;"
"\">\n"
"                Formulier\n"
"            </a>\n"
"        </div>\n"
"        Uw formulieren zijn beschikbaar op de pagina <a href=\"/my/"
"formio\">Mijn Formulieren</a> \n"
"    </p>\n"
"</div>\n"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_kanban
msgid ""
"<i class=\"fa fa-clock-o\" title=\"Submission date\" aria-label=\"Submission "
"date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" title=\"Ingediend op\" aria-label=\"Ingediend "
"op\"/>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_kanban
msgid ""
"<i class=\"fa fa-globe text-muted\"/><span class=\"text-muted\"> portal</"
"span>"
msgstr ""
"<i class=\"fa fa-globe text-muted\"/><span class=\"text-muted\"> portaal</"
"span>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "<i class=\"fa fa-info-circle\" title=\"info\"/>"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_server_action_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> <strong> Forms Ref:</strong> "
"Reference used in Form Builder(s) to indentiy the link between a Server "
"Action and Form Builder(s)."
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> <strong>Forms Ref:</strong>\n"
"Referentie gebruikt in Form Builder(s) om de link tussen een Serveractie en "
"Form Builder te identificeren."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> <strong>Public Current URL:</"
"strong> Public Form with versioning (same Name) in state Current"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/><strong> Openbare actuele "
"URL:</strong> Openbaar formulier met versiebeheer (dezelfde naam) in status "
"Actueel"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> <strong>Public URL:</strong> "
"Public Form for this form builder version"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/><strong> Openbare "
"URL:</strong> Openbaar formulier voor deze form builder versie."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Configuration of Website "
"features shall also be done here"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Configuratie van website \n"
"functionaliteiten zal ook hier staan."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Configure in Resource tab "
"(if settings are available)"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Configureer in Resource tab "
"\"\n"
"(indien instellingen aanwezig)"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Configure in related tabs "
"(Portal, Public / Website)"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Configureer in gerelateerde "
"tabbladen \"\n"
"(Portaal, Openbaar / Website)"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Download and install formio."
"js versions (menu: Configuration / formio.js versions)"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Download en installeer "
"formio.js versies (menu: Configuratie / formio.js versies)"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Enables adding the URL query "
"params from the window's iframe (src) or window.parent to the form's "
"submission (load) URL endpoint."
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Maakt het toevoegen van de "
"URL-query params mogelijk, van het iframe (src) of window.parent venster "
"naar het formulier submission (load) URL-eindpunt."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Enables adding the URL query "
"params from the window's iframe (src) or window.parent to the form's "
"submission (load) URL endpoint.\n"
"                                        <i class=\"fa fa-info-circle\" "
"title=\"info\"/> Similar setting for portal and public/website forms (see "
"tabs)."
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Maakt het toevoegen van de "
"URL-query params mogelijk, van het iframe (src) of window.parent venster "
"naar het formulier submission (load) URL-eindpunt."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Multi-page form, with "
"previous/next page buttons"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Formulier met meerdere "
"pagina's, met knoppen voor vorige/volgende pagina"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Not applicable with public/"
"website Forms and custom implementations (depends)."
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Dit is niet van toepassing\n"
"bij openbare (gepubliceerde) formulieren en custom implementaties."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Other <em>Public Access Rule "
"Type(s)</em> can be implemented by other modules."
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Andere <em>Publieke "
"toegangsregeltypes</em> kunnen geimplementeerd zijn door andere modules."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Server actions which process "
"the form data / submission<br/>\n"
"                                        <i class=\"fa fa-info-circle\" "
"title=\"info\"/> This is used by the"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Serveracties die de "
"formulier data/indiening vewerken<br/>\n"
"<i class=\"fa fa-info-circle\" title=\"info\"/> Dit wordt gebruikt door de"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Specific permissions on Forms"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Specifieke rechten op "
"Formulieren"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> The Base Translations can be "
"found in menu: Configuration / Translations.<br/>\n"
"                                <i class=\"fa fa-info-circle\" "
"title=\"info\"/> Action button ADD BASE TRANSLATIONS doesn't overwrite "
"changes.<br/>\n"
"                                <i class=\"fa fa-info-circle\" "
"title=\"info\"/> Action button UNLINK BASE TRANSLATIONS removes the Base "
"Translations from this formio.js version, but not the custom ones."
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> De basisvertalingen kunnen "
"gevonden in menu: Configuratie / Vertalingen.<br/>\n"
"<i class=\"fa fa-info-circle\" title=\"info\"/> Actieknop BASEVERTALINGEN "
"TOEVOEGEN overschrijft geen wijzigingen.<br/>\n"
"<i class=\"fa fa-info-circle\" title=\"info\"/> Actieknop BASISVERTALINGEN "
"ONTKOPPELEN verwijdert de basisvertalingen van deze formio.js-versie, maar "
"niet de custom vertalingen."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> To create or link (existing) "
"Partner with a Form submission, specify the fields (Email, Name) from the "
"Components API / Property Names.<br/>\n"
"                                    Partner determination/match shall be "
"done by Email. This API is especially useful for public Forms."
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Translations of the "
"<strong>formio.js version</strong> can be done via the General tab (click "
"field: formio.js version)<br/>\n"
"                                <i class=\"fa fa-info-circle\" "
"title=\"info\"/> The button \"Translations\" has the same as in this list."
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/> Vertalingen van de "
"<strong>formio.js versie</strong> worden gedaan via het tabblad Algemeen "
"(klik op veld: versie formio.js)<br/>\n"
"<i class=\"fa fa-info-circle\" title=\"info\"/> De knop \"Vertalingen\" "
"hebben dezelfe records als in deze lijst."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/><strong> Documentation</"
"strong><br/>\n"
"                                For example, Options could contain the Form "
"Builder editForm with some File component settings:<br/>"
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/><strong>Documentatie</"
"strong><br/>\n"
"Opties kunnen bijvoorbeeld de Form Builder editForm met enkele file "
"component instellingen bevatten:<br/>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<i class=\"fa fa-info-circle\" title=\"info\"/><strong> Expire After:</"
"strong> Applicable on the Form it's (datetime field) <strong>Public Access "
"From</strong>."
msgstr ""
"<i class=\"fa fa-info-circle\" title=\"info\"/><strong> Verloopt na:</"
"strong> Van toepassing op het formulier (datetime veld) <strong>Openbare "
"toegang vanaf</strong>."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "<i class=\"fa fa-link\"/> Deploy Tool (formio_deploy) module"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_kanban
msgid ""
"<i class=\"fa fa-user\" title=\"Submission partner\" aria-label=\"Submission "
"partner\"/>"
msgstr ""
"<i class=\"fa fa-user\" title=\"Ingediend door\" aria-label=\"Ingediend "
"door\"/>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid ""
"<span aria-label=\"Cancel &amp; close Form\" title=\"Cancel &amp; close "
"Form\" confirm=\"Are you sure?\">Cancel Form</span>"
msgstr ""
"<span aria-label=\"Annuleren\" title=\"Annuleren\" confirm=\"Bent u zeker?"
"\">Annuleren</span>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid "<span aria-label=\"Copy form\" title=\"Copy form\">Copy Form</span>"
msgstr ""
"<span aria-label=\"Kopieer formulier\" title=\"Mijn formulieren\">Kopieer "
"formulier</span>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid ""
"<span aria-label=\"Delete form\" title=\"Delete form\">Delete Form</span>"
msgstr ""
"<span aria-label=\"Verwijderen\" title=\"Verwijderen\">Verwijderen</span>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid "<span aria-label=\"Edit form\" title=\"Edit form\">Edit Form</span>"
msgstr "<span aria-label=\"Bewerken\" title=\"Bewerken\">Bewerken</span>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid "<span aria-label=\"View form\" title=\"View form\">View Form</span>"
msgstr "<span aria-label=\"Weergeven\" title=\"Weergeven\">Weergeven</span>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "<span>Form Builder</span>"
msgstr "<span>Form Builder</span>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "<span>Form</span>"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"<strong class=\"text-danger\">\n"
"                                            <i class=\"fa fa-info-circle\" "
"title=\"info\"/> Replace this \"Dummy\" version, which doesn't work<br/>\n"
"                                            <i class=\"fa fa-info-circle\" "
"title=\"info\"/> Dummy version is only here to bootstrap (default) demo "
"data<br/>\n"
"                                            <i class=\"fa fa-info-circle\" "
"title=\"info\"/> Download and install a formio.js version (menu: "
"Configuration / formio.js versions)\n"
"                                        </strong>"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"<strong>Example below</strong>. Options could contain the Form Builder "
"\"editForm\" with some \"File\" component settings:<br/>"
msgstr ""
"<strong>Voorbeeld hieronder</strong>. Opties kunnen Form Builder \n"
"\"editForm\" met \"File\" component instellingen bevatten:<br/>"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_kanban
msgid "<strong>Submission:</strong>"
msgstr "<strong>Indiening:</strong>"

#. module: formio
#. odoo-python
#: code:addons/formio/models/ir_actions.py:0
#, python-format
msgid ""
"A Server Action with Forms Ref \"%s\" already exists.\n"
"Forms Ref should be unique."
msgstr ""
"Er bestaat al een serveractie met Forms Ref \"%s\".\n"
"Forms Ref moet uniek zijn."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"A comma separated string (list) of formio.js versions to register.\n"
"                                The strings must conform to the format of "
"the release names:"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_translation_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_translation_form
msgid "A formio.js library translation property ..."
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_version_translation__source_property
msgid ""
"A formio.js library translation property, eg 'submit', 'cancel', 'error', "
"'previous'"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"A scheduled action (daily cron) is available and already active.<br/>\n"
"                                        Releases:"
msgstr ""
"Een geplande actie (dagelijkse cron) is beschikbaar en al actief.<br/>\n"
"Releases:"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Access"
msgstr "Toegang"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__act_window_url
#: model:ir.model.fields,field_description:formio.field_formio_form__act_window_url
msgid "Act Window Url"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_needaction
#: model:ir.model.fields,field_description:formio.field_formio_form__message_needaction
#: model:ir.model.fields,field_description:formio.field_formio_license__message_needaction
#: model:ir.model.fields,field_description:formio.field_formio_version__message_needaction
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_needaction
msgid "Action Needed"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Actions"
msgstr "Acties"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Actions API"
msgstr "Acties API"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__active
#: model:ir.model.fields,field_description:formio.field_formio_license__active
#: model:ir.model.fields,field_description:formio.field_formio_version__active
msgid "Active"
msgstr "Actief"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_ids
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_ids
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_ids
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_ids
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_ids
msgid "Activities"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_exception_decoration
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_exception_decoration
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_exception_decoration
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_exception_decoration
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_state
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_state
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_state
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_state
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_state
msgid "Activity State"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_type_icon
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_type_icon
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_type_icon
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_type_icon
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: formio
#: model:ir.actions.act_window,name:formio.mail_activity_type_action_config_formio
#: model:ir.ui.menu,name:formio.formio_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Activity User"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_form
msgid "Add Base Translations"
msgstr "Basis vertalingen toevoegen"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Add Query Params to Submission URL from"
msgstr "Query parameters toevoegen aan indienings (submission) URL van"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"Add additional CSS files to be included for every new installed formio.js "
"client/library."
msgstr ""
"Voeg extra CSS bestanden toe voor elke nieuw geïnstalleerde formio.js client/"
"library."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__component_partner_add_follower
msgid "Add determined partner to followers of the Form."
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Add to Followers"
msgstr ""

#. module: formio
#: model:res.groups,name:formio.group_formio_admin
msgid "Administrator"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__form_allow_copy
#: model:ir.model.fields,field_description:formio.field_formio_form__allow_copy
msgid "Allow Copies"
msgstr "Kopieren toestaan"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__form_allow_copy
#: model:ir.model.fields,help:formio.field_formio_form__allow_copy
msgid "Allow copying form submissions."
msgstr "Sta het kopiëren van formulierinzendingen toe."

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__allow_unlink
msgid "Allow delete"
msgstr "Verwijderen toestaan"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__allow_force_update_state
msgid "Allow force update State"
msgstr "Sta geforceerde status update toe"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__allow_force_update_state_group_ids
msgid "Allow groups to force update State"
msgstr "Groepen toestaan om een status update te doen"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__backend_use_draft
msgid ""
"Allows to use this Form Builder in state Draft, when adding/choosing a new "
"Form in the backend."
msgstr ""
"Maakt het gebruik van deze Form Builder in status Draft mogelijk bij het "
"toevoegen/kiezen van een nieuw Formulier in de backend."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__backend_use_obsolete
msgid ""
"Allows to use this Form Builder in state Obsolete, when adding/choosing a "
"new Form in the backend."
msgstr ""
"Maakt het gebruik van deze Form Builder in status Obsolete mogelijk bij het "
"toevoegen/kiezen van een nieuw Formulier in de backend."

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__api_alert
msgid "Api Alert"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__archive_url
msgid "Archive URL"
msgstr "Archief URL"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_license_search
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"Are you sure to check for available versions? This could take some time to "
"load, please be patient."
msgstr ""
"Weet je zeker dat je wilt controleren op beschikbare versies? Dit kan even "
"duren, wees geduldig."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_form
msgid ""
"Are you sure to download and install? This could take some time to load, "
"please be patient."
msgstr ""
"Weet je zeker dat je wilt downloaden en installeren? Dit kan even duren, "
"wees geduldig."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_form
msgid "Are you sure to reset (download and reinstall)?"
msgstr ""
"Weet je zeker dat je wilt resetten (downloaden en opnieuw installeren)?"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_form
msgid ""
"Are you sure to unlink the base translations from this formio.js version?"
msgstr ""
"Weet je zeker dat je de basisvertalingen wilt ontkoppelen van deze formio.js "
"versie?"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"Are you sure? Modifying the Form Builder could cause existing forms to be "
"invalid."
msgstr ""
"Weet je het zeker? Het aanpassen van de Form Builder kan ertoe leiden dat "
"bestaande formulieren niet meer functioneren."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"Are you sure? No further modifications are possible in the Form Builder and "
"configuration."
msgstr ""
"Weet je het zeker? Er zijn dan geen wijzigingen mogelijk in de Form Builder "
"en de meeste configuratie."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Are you sure? The Form shall be unpublished."
msgstr "Weet je het zeker? Het formulier wordt niet (meer) gepubliceerd."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Are you sure? This creates a new (draft) version."
msgstr "Weet je het zeker? Dit maakt een nieuwe (concept) versie."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Are you sure? This makes it possible to create and use these forms."
msgstr ""
"Weet je het zeker? Dit maakt het mogelijk om deze formulieren te gebruiken."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_form
msgid "Assets (JavaScript, CSS, Fonts)"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version__assets
msgid "Assets (js, css)"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Assigned Forms"
msgstr "Toegewezen formulieren"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__assigned_partner_id
msgid "Assigned Partner"
msgstr "Toegewezen partner"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__assigned_partner_name
msgid "Assigned Partner Name"
msgstr "Toegewezen partner naam"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Assigned User"
msgstr "Toegewezen gebruiker"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__user_id
#: model:ir.model.fields,field_description:formio.field_formio_form__user_id
msgid "Assigned user"
msgstr "Toegewezen gebruiker"

#. module: formio
#: model:ir.model,name:formio.model_ir_attachment
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__attachment_id
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__attachment_id
msgid "Attachment"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_attachment_count
#: model:ir.model.fields,field_description:formio.field_formio_form__message_attachment_count
#: model:ir.model.fields,field_description:formio.field_formio_license__message_attachment_count
#: model:ir.model.fields,field_description:formio.field_formio_version__message_attachment_count
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__attachment_type
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__attachment_type
msgid "Attachment Type"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__auto_save
msgid "Auto Save"
msgstr ""

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#, python-format
msgid "Auto-save"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__auto_save
msgid "Auto-save or manually save the Form Builder changes"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_github_tag__state__available
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_search
msgid "Available"
msgstr "Beschikbaar"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__available_version_github_tag_ids
msgid "Available Version Github Tag"
msgstr "Beschikbare versie GitHub tag"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_checker_wizard
msgid "Available Versions (releases)"
msgstr "Beschikbare versies (releases)"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__backend_submission_url_add_query_params_from
msgid "Backend Add Query Params to Submission URL from"
msgstr "Backend query parameters toevoegen aan indienings (submission) URL van"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__builder_id_domain
msgid "Builder Id Domain"
msgstr "Builder Id domein"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__builder_name
msgid "Builder Name"
msgstr "Builder Naam"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__builder_version
msgid "Builder Version"
msgstr "Builder versie"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__css
msgid "CSS"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version__css_assets
msgid "CSS Assets"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_checker_wizard
msgid "Cancel"
msgstr "Annuleer"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_form__kanban_group_state__d
#: model:ir.model.fields.selection,name:formio.selection__formio_form__state__cancel
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Canceled"
msgstr "Geannuleerd"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__changelog_url
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__changelog_url
msgid "Changelog URL"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Check and Register available Versions"
msgstr "Controleer en registreer beschikbare versies"

#. module: formio
#. odoo-python
#: code:addons/formio/models/res_config_settings.py:0
#, python-format
msgid "Check and Register new Versions"
msgstr "Controleer en registreer nieuwe versies"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Check, register and install the latest 30 available Versions"
msgstr "Controleer, registreer en installeer de laatste 30 beschikbare versies"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Complete"
msgstr "Voltooi"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_form__kanban_group_state__c
#: model:ir.model.fields.selection,name:formio.selection__formio_form__state__complete
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Completed"
msgstr "Voltooid"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__component_partner_activity_user_id
msgid "Component Partner Activity User"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__component_partner_add_follower
msgid "Component Partner Add to Followers"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__component_partner_email
msgid "Component Partner Email"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__component_partner_name
msgid "Component Partner Name"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Components"
msgstr "Componenten"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Components API"
msgstr "Componenten API"

#. module: formio
#: model:ir.model,name:formio.model_res_config_settings
msgid "Config Settings"
msgstr "Config Instellingen"

#. module: formio
#: model:ir.ui.menu,name:formio.menu_formio_configuration
msgid "Configuration"
msgstr "Configuratie"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__form_copy_to_current
#: model:ir.model.fields,field_description:formio.field_formio_form__copy_to_current
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Copy To Current"
msgstr "Kopieer naar actueel"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Create New Version"
msgstr "Maak nieuwe versie"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_builder_js_options__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_form__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_license__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_res_model__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_translation__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_translation_source__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_version__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__create_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__create_date
#: model:ir.model.fields,field_description:formio.field_formio_builder_js_options__create_date
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__create_date
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__create_date
#: model:ir.model.fields,field_description:formio.field_formio_form__create_date
#: model:ir.model.fields,field_description:formio.field_formio_license__create_date
#: model:ir.model.fields,field_description:formio.field_formio_res_model__create_date
#: model:ir.model.fields,field_description:formio.field_formio_translation__create_date
#: model:ir.model.fields,field_description:formio.field_formio_translation_source__create_date
#: model:ir.model.fields,field_description:formio.field_formio_version__create_date
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__create_date
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__create_date
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__create_date
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__create_date
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__create_date
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid "Created on"
msgstr "Aangemaakt op"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__state__current
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
msgid "Current"
msgstr "Actueel"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__current_uuid
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
msgid "Current UUID"
msgstr "Actuele UUID"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__readonly_submission_data
msgid "Data is readonly"
msgstr "Data is alleen-lezen"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__initial_res_id
#: model:ir.model.fields,help:formio.field_formio_form__res_id
msgid "Database ID of the record in res_model to which this applies"
msgstr "Database-ID van het record in res_model waarop dit van toepassing is"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__public_access_date_from
msgid "Datetime from when the form is public shared until it expires."
msgstr ""
"Datum/tijd vanaf het moment dat het formulier openbaar is gedeeld totdat het "
"verloopt."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__submission_date
msgid "Datetime when the form was last submitted."
msgstr "Datum/tijd wanneer het formulier voor het laatst was ingediend."

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__public_access_interval_type__days
#: model:ir.model.fields.selection,name:formio.selection__formio_form__public_access_interval_type__days
msgid "Days"
msgstr "Dagen"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__debug
msgid "Debug"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__debug_mode
#: model:ir.model.fields,field_description:formio.field_formio_form__debug_mode
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Debug Mode"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Default CSS assets"
msgstr "Standaard CSS assets"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Default Form Builder formio.js Options Template"
msgstr "Standaard Form Builder formio.js opties tempalte"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Default Version"
msgstr "Standaard versie"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__description
#: model:ir.model.fields,field_description:formio.field_formio_version__description
msgid "Description"
msgstr "Omschrijving"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Description..."
msgstr "Omschrijving..."

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#, python-format
msgid "Disabled"
msgstr "Uitgeschakeld"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Display"
msgstr "Weergave"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__display_name
#: model:ir.model.fields,field_description:formio.field_formio_builder_js_options__display_name
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__display_name
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__display_name
#: model:ir.model.fields,field_description:formio.field_formio_form__display_name
#: model:ir.model.fields,field_description:formio.field_formio_license__display_name
#: model:ir.model.fields,field_description:formio.field_formio_res_model__display_name
#: model:ir.model.fields,field_description:formio.field_formio_translation__display_name
#: model:ir.model.fields,field_description:formio.field_formio_translation_source__display_name
#: model:ir.model.fields,field_description:formio.field_formio_version__display_name
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__display_name
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__display_name
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__display_name
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__display_name
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__display_name
msgid "Display Name"
msgstr "Weergave Naam"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__display_name_full
msgid "Display Name Full"
msgstr "Weergave naam volledig"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__display_state
#: model:ir.model.fields,field_description:formio.field_formio_form__display_state
msgid "Display State"
msgstr "Stauts weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_license__domains
msgid "Domains"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_form
msgid "Download and Install"
msgstr "Downloaden en installeren"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__state__draft
#: model:ir.model.fields.selection,name:formio.selection__formio_form__kanban_group_state__b
#: model:ir.model.fields.selection,name:formio.selection__formio_form__state__draft
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Draft"
msgstr "Concept"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid "Dropdown menu"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__eot
msgid "EOT Font File"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__edit_url
msgid "Edit Url"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Email"
msgstr ""

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#, python-format
msgid "Enabled"
msgstr "Ingeschakeld"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__backend_submission_url_add_query_params_from
#: model:ir.model.fields,help:formio.field_formio_builder__portal_submission_url_add_query_params_from
#: model:ir.model.fields,help:formio.field_formio_builder__public_submission_url_add_query_params_from
msgid ""
"Enables adding the URL query params from the window's iframe (src) or window."
"parent to the form submission URL endpoint."
msgstr ""
"Maakt het toevoegen van de URL query parameters uit het iframe (src) venster "
"of bovenliggende venster mogelijk, naar het formulier indien(submission) "
"endpoint URL."

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__language_en_enable
msgid "English Enabled"
msgstr "Engels ingeschakeld"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__portal_scroll_into_view_selector
#: model:ir.model.fields,help:formio.field_formio_builder__public_scroll_into_view_selector
msgid ""
"Especially for long wizard pages upon prev/next page. This scrolls an "
"element (CSS selector) into the visible area of the browser window."
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Expire After"
msgstr "Verloopt na"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Faaoaoaoa"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_follower_ids
#: model:ir.model.fields,field_description:formio.field_formio_form__message_follower_ids
#: model:ir.model.fields,field_description:formio.field_formio_license__message_follower_ids
#: model:ir.model.fields,field_description:formio.field_formio_version__message_follower_ids
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_follower_ids
msgid "Followers"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_partner_ids
#: model:ir.model.fields,field_description:formio.field_formio_form__message_partner_ids
#: model:ir.model.fields,field_description:formio.field_formio_license__message_partner_ids
#: model:ir.model.fields,field_description:formio.field_formio_version__message_partner_ids
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__activity_type_icon
#: model:ir.model.fields,help:formio.field_formio_form__activity_type_icon
#: model:ir.model.fields,help:formio.field_formio_license__activity_type_icon
#: model:ir.model.fields,help:formio.field_formio_version__activity_type_icon
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_kanban
#: model_terms:ir.ui.view,arch_db:formio.view_formio_license_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_res_model_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_form
msgid "Form"
msgstr "Form"

#. module: formio
#: model:ir.model,name:formio.model_formio_builder
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__builder_id
#: model:ir.model.fields,field_description:formio.field_formio_form__builder_id
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Form Builder"
msgstr "Form Builder"

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_builder_translation.py:0
#, python-format
msgid ""
"Form Builder Translations must be unique.\n"
"\n"
"%s"
msgstr ""
"Form Builder vertalingen moeten uniek zijn.\n"
"\n"
"%s"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_builder_js_options
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_js_options_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_js_options_tree
msgid "Form Builder formio.js Options"
msgstr "Form Builder formio.js opties"

#. module: formio
#: model:ir.ui.menu,name:formio.menu_formio_builder_js_options
msgid "Form Builder formio.js options"
msgstr "Form Builder formio.js opties"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_builder
#: model:ir.ui.menu,name:formio.menu_formio_builder
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_tree
msgid "Form Builders"
msgstr "Form Builders"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Form Display"
msgstr "Formulier weergave"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Form Permissions"
msgstr "Formulier rechten"

#. module: formio
#: model:ir.model,name:formio.model_formio_res_model
msgid "Form Resource Model"
msgstr ""

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_form
#: model:ir.ui.menu,name:formio.menu_formio_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_tree
msgid "Form Submissions"
msgstr "Forms Inzendingen"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__portal
#: model:ir.model.fields,help:formio.field_formio_form__portal
msgid "Form is accessible by assigned portal user"
msgstr "Formulier is toegankelijk voor toegewezen portaal gebruiker."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__public
#: model:ir.model.fields,help:formio.field_formio_form__public
msgid ""
"Form is public accessible (e.g. used in Shop checkout, Events registration"
msgstr ""
"Formulier is openbaar toegankelijk (bijv. gebruikt bij de checkout in de "
"shop, registratie van evenementen)"

#. module: formio
#: model:mail.activity.type,name:formio.mail_act_partner_linking
msgid "Form linking to Partner"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__public_create
msgid "Form was public created"
msgstr "Formulier was openbaar"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_search
msgid "Formio Versions"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__forms
#: model:ir.ui.menu,name:formio.menu_formio_root
#: model_terms:ir.ui.view,arch_db:formio.portal_layout
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_home
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Forms"
msgstr "Formulieren"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__forms_count
msgid "Forms Count"
msgstr "Formulieren aantal"

#. module: formio
#: model:ir.model,name:formio.model_formio_license
msgid "Forms License"
msgstr "Forms Licentie"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_license_search
msgid "Forms Licenses"
msgstr "Forms Licenties"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__attachment_formio_ref
#: model:ir.model.fields,field_description:formio.field_ir_actions_server__formio_ref
#: model:ir.model.fields,field_description:formio.field_ir_attachment__formio_ref
#: model:ir.model.fields,field_description:formio.field_ir_cron__formio_ref
msgid "Forms Ref"
msgstr "Forms Ref"

#. module: formio
#. odoo-python
#: code:addons/formio/models/ir_actions.py:0
#: code:addons/formio/models/ir_attachment.py:0
#, python-format
msgid "Forms Ref is invalid. Use ASCII letters, digits, \"-\" or \"_\"."
msgstr "Forms Ref is ongeldig. Gebruik ASCII letters, cijfers, \"-\" of \"_\"."

#. module: formio
#: model:res.groups,name:formio.group_formio_form_update
msgid "Forms: Allow updating form submission data"
msgstr "Forms: Sta het bijwerken van de formulier datum van inzending toe."

#. module: formio
#: model:ir.actions.server,name:formio.ir_cron_formio_version_github_tag_check_and_register_ir_actions_server
msgid "Forms: Check and register new Versions (GitHub tags)"
msgstr "Forms: Controleer en registreer nieuwe versies (GitHub tags)"

#. module: formio
#: model:mail.template,name:formio.mail_invitation_internal_user
msgid "Forms: Invitation internal user"
msgstr "Forms: Uitnodiging interne gebruiker"

#. module: formio
#: model:mail.template,name:formio.mail_invitation_portal_user
msgid "Forms: Invitation portal user"
msgstr "Forms: Uitnodiging portaal gebruiker."

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_form.py:0
#, python-format
msgid ""
"Found multiple Partners with email <strong>%s</strong> submitted in the Form."
msgstr ""

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#, python-format
msgid "Fullscreen (Exit with ESC)"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "General"
msgstr "Algemeen"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__github_tag_ids
msgid "GitHub Tags"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_res_config_settings__formio_github_personal_access_token
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "GitHub personal access token"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_search
msgid "Group By"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__has_message
#: model:ir.model.fields,field_description:formio.field_formio_form__has_message
#: model:ir.model.fields,field_description:formio.field_formio_license__has_message
#: model:ir.model.fields,field_description:formio.field_formio_version__has_message
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__has_message
msgid "Has Message"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__hook_api_validation
msgid "Hook Validation API"
msgstr "Hook Validatie API"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__public_access_interval_type__hours
#: model:ir.model.fields.selection,name:formio.selection__formio_form__public_access_interval_type__hours
msgid "Hours"
msgstr "Uren"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__id
#: model:ir.model.fields,field_description:formio.field_formio_builder_js_options__id
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__id
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__id
#: model:ir.model.fields,field_description:formio.field_formio_form__id
#: model:ir.model.fields,field_description:formio.field_formio_license__id
#: model:ir.model.fields,field_description:formio.field_formio_res_model__id
#: model:ir.model.fields,field_description:formio.field_formio_translation__id
#: model:ir.model.fields,field_description:formio.field_formio_translation_source__id
#: model:ir.model.fields,field_description:formio.field_formio_version__id
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__id
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__id
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__id
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__id
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__id
msgid "ID"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_kanban
msgid "ID:"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_res_lang__formio_ietf_code
msgid "IETF Code"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_exception_icon
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_exception_icon
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_exception_icon
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_exception_icon
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__activity_exception_icon
#: model:ir.model.fields,help:formio.field_formio_form__activity_exception_icon
#: model:ir.model.fields,help:formio.field_formio_license__activity_exception_icon
#: model:ir.model.fields,help:formio.field_formio_version__activity_exception_icon
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_ir_actions_server__formio_ref
#: model:ir.model.fields,help:formio.field_ir_cron__formio_ref
msgid "Identifies a server action with related form builder."
msgstr "Identificeert een serveractie met gerelateerde Form Builder."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_version_asset__attachment_formio_ref
#: model:ir.model.fields,help:formio.field_ir_attachment__formio_ref
msgid "Identifies an attachment with a related Forms/Builder record."
msgstr "Identificeert een bijlage met een gerelateerd Forms Builder."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__name
#: model:ir.model.fields,help:formio.field_formio_builder_translation__builder_name
#: model:ir.model.fields,help:formio.field_formio_form__name
msgid ""
"Identifies this specific form. This name can be used in APIs.         Use "
"only ASCII letters, digits, \"-\" or \"_\"."
msgstr ""
"Identificeert dit specifieke formulier. Deze naam kan worden gebruikt in "
"API's.\n"
"Gebruik alleen ASCII letters, cijfers, \"-\" of \"_\"."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__message_needaction
#: model:ir.model.fields,help:formio.field_formio_form__message_needaction
#: model:ir.model.fields,help:formio.field_formio_license__message_needaction
#: model:ir.model.fields,help:formio.field_formio_version__message_needaction
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__message_has_error
#: model:ir.model.fields,help:formio.field_formio_builder__message_has_sms_error
#: model:ir.model.fields,help:formio.field_formio_form__message_has_error
#: model:ir.model.fields,help:formio.field_formio_form__message_has_sms_error
#: model:ir.model.fields,help:formio.field_formio_license__message_has_error
#: model:ir.model.fields,help:formio.field_formio_license__message_has_sms_error
#: model:ir.model.fields,help:formio.field_formio_version__message_has_error
#: model:ir.model.fields,help:formio.field_formio_version__message_has_sms_error
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__message_has_error
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Info"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Insert a meaningful title here"
msgstr "Voer hier een betekenisvolle titel in"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_github_tag__state__installed
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_search
msgid "Installed"
msgstr "Geïnstalleerd"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__installed_version_ids
msgid "Installed Versions"
msgstr "Geïnstalleerde versies"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_version
#: model:ir.ui.menu,name:formio.menu_formio_version
msgid "Installed formio.js versions"
msgstr "Geïnstalleerde formio.js versies"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__install_date
msgid "Installed on"
msgstr "Geïnstalleerd op"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Integration"
msgstr "Integratie"

#. module: formio
#. odoo-python
#: code:addons/formio/models/res_config_settings.py:0
#, python-format
msgid "Invalid formio.js version name: %s"
msgstr "Ongeldige formio.js versie naam: %s"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__invitation_mail_template_id
msgid "Invitation Mail"
msgstr "Uitnodigingsmail"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__formio_version_is_dummy
#: model:ir.model.fields,field_description:formio.field_formio_version__is_dummy
msgid "Is Dummy (default version in demo data)"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_is_follower
#: model:ir.model.fields,field_description:formio.field_formio_form__message_is_follower
#: model:ir.model.fields,field_description:formio.field_formio_license__message_is_follower
#: model:ir.model.fields,field_description:formio.field_formio_version__message_is_follower
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "JS Options"
msgstr "JS opties"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__schema
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "JSON Schema"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__js
msgid "JavaScript"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version__js_assets
msgid "Javascript Assets"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__kanban_group_state
msgid "Kanban Group State"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_license__key
msgid "Key"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__lang_id
#: model:ir.model.fields,field_description:formio.field_formio_translation__lang_id
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__lang_id
msgid "Language"
msgstr "Taal"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__lang_name
msgid "Language Name"
msgstr "Taal naam"

#. module: formio
#: model:ir.model,name:formio.model_res_lang
#: model:ir.model.fields,field_description:formio.field_formio_builder__languages
#: model:ir.model.fields,field_description:formio.field_formio_form__languages
msgid "Languages"
msgstr "Talen"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Languages & Locales"
msgstr "Talen en localisaties"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_builder_js_options__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_form__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_license__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_res_model__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_translation__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_translation_source__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_version__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__write_uid
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__write_date
#: model:ir.model.fields,field_description:formio.field_formio_builder_js_options__write_date
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__write_date
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__write_date
#: model:ir.model.fields,field_description:formio.field_formio_form__write_date
#: model:ir.model.fields,field_description:formio.field_formio_license__write_date
#: model:ir.model.fields,field_description:formio.field_formio_res_model__write_date
#: model:ir.model.fields,field_description:formio.field_formio_translation__write_date
#: model:ir.model.fields,field_description:formio.field_formio_translation_source__write_date
#: model:ir.model.fields,field_description:formio.field_formio_version__write_date
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__write_date
#: model:ir.model.fields,field_description:formio.field_formio_version_github_checker_wizard__write_date
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__write_date
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__write_date
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__license
#: model_terms:ir.ui.view,arch_db:formio.view_formio_license_tree
msgid "License"
msgstr "Licentie"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version__license_assets
msgid "License Assets"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_form
msgid "License(s)"
msgstr "Licentie(s)"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_license
#: model:ir.ui.menu,name:formio.menu_formio_license
msgid "Licenses"
msgstr "Licenties"

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_form.py:0
#, python-format
msgid "Link the Form to the appropriate Partner"
msgstr "Koppel het formulier aan de juiste partner"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__formio_version_id
msgid ""
"Loads the specific formio.js Javascript libraries version (sourcecode: "
"https://github.com/formio/formio.js)"
msgstr ""
"Laadt de specifieke formio.js Javascript library versie (broncode: https://"
"github.com/formio/formio.js)"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Lock"
msgstr "Vergrendel"

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#: model:ir.model.fields,field_description:formio.field_formio_builder__is_locked
#, python-format
msgid "Locked"
msgstr "Vergrendeld"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_has_error
#: model:ir.model.fields,field_description:formio.field_formio_form__message_has_error
#: model:ir.model.fields,field_description:formio.field_formio_license__message_has_error
#: model:ir.model.fields,field_description:formio.field_formio_version__message_has_error
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_ids
#: model:ir.model.fields,field_description:formio.field_formio_form__message_ids
#: model:ir.model.fields,field_description:formio.field_formio_license__message_ids
#: model:ir.model.fields,field_description:formio.field_formio_version__message_ids
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_ids
msgid "Messages"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__public_access_interval_type__minutes
#: model:ir.model.fields.selection,name:formio.selection__formio_form__public_access_interval_type__minutes
msgid "Minutes"
msgstr "Minuten"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Miscellaneous"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__res_model_id
#: model:ir.model.fields,field_description:formio.field_formio_res_model__ir_model_id
msgid "Model"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__formio_res_model_id
#: model:ir.model.fields,help:formio.field_formio_builder__res_model_id
#: model:ir.model.fields,help:formio.field_formio_form__initial_res_model_id
msgid "Model as resource this form represents or acts on"
msgstr "Model als resource waarop dit formilier van toepassing is"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_res_model__module_dependency
msgid "Module Dependency"
msgstr "Module afhankelijkheid"

#. module: formio
#: model:ir.model.fields,help:formio.field_ir_attachment__formio_asset_formio_version_id
msgid ""
"Mostly files from the formio.js project - https://github.com/formio/formio.js"
msgstr ""
"Vooral bestanden van het formio.js project - https://github.com/formio/"
"formio.js"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__my_activity_date_deadline
#: model:ir.model.fields,field_description:formio.field_formio_form__my_activity_date_deadline
#: model:ir.model.fields,field_description:formio.field_formio_license__my_activity_date_deadline
#: model:ir.model.fields,field_description:formio.field_formio_version__my_activity_date_deadline
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "My Forms"
msgstr "Mijn Formulieren"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__name
#: model:ir.model.fields,field_description:formio.field_formio_builder_js_options__name
#: model:ir.model.fields,field_description:formio.field_formio_form__name
#: model:ir.model.fields,field_description:formio.field_formio_version__name
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__name
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__name
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
msgid "Name"
msgstr "Naam"

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_builder.py:0
#, python-format
msgid "Name is invalid. Use ASCII letters, digits, \"-\" or \"_\"."
msgstr "Naam is ongeldig. Gebruik ASCII letters, cijfers, - of _"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_date_deadline
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_date_deadline
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_date_deadline
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_date_deadline
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_summary
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_summary
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_summary
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_summary
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_type_id
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_type_id
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_type_id
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_type_id
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__nodelete
msgid "No delete (core)"
msgstr "Geen verwijdering (core/module)"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
msgid "Not Portal"
msgstr "Niet Portaal"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_translation_search
msgid "Not Updated Base Translations"
msgstr "Niet bijgewerkte basis vertalingen"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
msgid "Not Wizard"
msgstr "Niet Wizard"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_needaction_counter
#: model:ir.model.fields,field_description:formio.field_formio_form__message_needaction_counter
#: model:ir.model.fields,field_description:formio.field_formio_license__message_needaction_counter
#: model:ir.model.fields,field_description:formio.field_formio_version__message_needaction_counter
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_has_error_counter
#: model:ir.model.fields,field_description:formio.field_formio_form__message_has_error_counter
#: model:ir.model.fields,field_description:formio.field_formio_license__message_has_error_counter
#: model:ir.model.fields,field_description:formio.field_formio_version__message_has_error_counter
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__message_needaction_counter
#: model:ir.model.fields,help:formio.field_formio_form__message_needaction_counter
#: model:ir.model.fields,help:formio.field_formio_license__message_needaction_counter
#: model:ir.model.fields,help:formio.field_formio_version__message_needaction_counter
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__message_has_error_counter
#: model:ir.model.fields,help:formio.field_formio_form__message_has_error_counter
#: model:ir.model.fields,help:formio.field_formio_license__message_has_error_counter
#: model:ir.model.fields,help:formio.field_formio_version__message_has_error_counter
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__otf
msgid "OTF Font File"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__state__obsolete
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
msgid "Obsolete"
msgstr "Verouderd"

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_builder.py:0
#, python-format
msgid "Only one Form Builder with name \"{name}\" can be in state Current."
msgstr ""
"Er mag maar 1 Form Builder met de naam \"{name}, de status Actueel hebben."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_tree
msgid "Open Form"
msgstr "Open formulier"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Options Template"
msgstr "Opties template"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__base_translation_origin
msgid "Origin Base"
msgstr "Originele basis"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_translation_search
msgid "Origin Base Translations"
msgstr "Originele basis Vertalingen"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_translation_search
msgid "Origin User Added Translations"
msgstr "Originele door gebruiker toegevoegde vertalingen"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Other"
msgstr "Andere"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Other Component settings"
msgstr "Overige component instellingen"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__overlay_api_change
msgid "Overlay Change API"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__parent_id
msgid "Parent Builder"
msgstr "Vorige Builder"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__parent_version
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Parent Version"
msgstr "Vorige Versie"

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#, python-format
msgid "Parent version"
msgstr "Vorige Versie"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__partner_id
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Partner"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__assigned_partner_id
#: model:ir.model.fields,help:formio.field_formio_form__submission_partner_id
msgid "Partner-related data of the user"
msgstr "Partner gerelateerde data van de gebruiker"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_form__kanban_group_state__a
#: model:ir.model.fields.selection,name:formio.selection__formio_form__state__pending
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Pending"
msgstr "In afwachting"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Personal access token"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__portal
#: model:ir.model.fields,field_description:formio.field_formio_form__portal_share
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
msgid "Portal"
msgstr "Portaal"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__portal
msgid "Portal (Builder)"
msgstr "Portal (Builder)"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__portal_submission_url_add_query_params_from
msgid "Portal Add Query Params to Submission URL from"
msgstr "Portal query parameters toevoegen aan indienings (submission) URL van"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__portal_save_draft_done_url
#: model:ir.model.fields,field_description:formio.field_formio_form__portal_save_draft_done_url
msgid "Portal Save-Draft Done URL"
msgstr "Portaal URL na concept opslaan"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__portal_scroll_into_view_selector
msgid "Portal Scroll Into View Selector"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__portal_submit_done_url
#: model:ir.model.fields,field_description:formio.field_formio_form__portal_submit_done_url
msgid "Portal Submit Done URL"
msgstr "Portal URL na indiening"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__portal_url
msgid "Portal URL"
msgstr "Portaal URL"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_translation__property
#: model:ir.model.fields,field_description:formio.field_formio_translation_source__property
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__source_property
msgid "Property"
msgstr "Eigenschap"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public
#: model:ir.model.fields,field_description:formio.field_formio_form__public_share
msgid "Public"
msgstr "Openbaar"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__public
msgid "Public (Builder)"
msgstr "Gepubliceerde (Builder)"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Public / Website"
msgstr "Openbaar / website"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__public_access
msgid "Public Access"
msgstr "Openbare toegang"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Public Access Expire"
msgstr "Openbare toegang verloopt"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__public_access_date_from
msgid "Public Access From"
msgstr "Openbare toegang vanaf"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_access_interval_number
#: model:ir.model.fields,field_description:formio.field_formio_form__public_access_interval_number
msgid "Public Access Interval Number"
msgstr "Openbare toegang intervalnummer"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_access_interval_type
#: model:ir.model.fields,field_description:formio.field_formio_form__public_access_interval_type
msgid "Public Access Interval Type"
msgstr "Openbare toegang intervaltype"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_access_rule_type
#: model:ir.model.fields,field_description:formio.field_formio_form__public_access_rule_type
msgid "Public Access Rule Type"
msgstr "Openbare toegang type"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_submission_url_add_query_params_from
msgid "Public Add Query Params to Submission URL from"
msgstr ""
"Openbare/website query parameters toevoegen aan indienings (submission) URL "
"van"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__public_create
msgid "Public Created"
msgstr "Openbaar ingediend"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_current_url
msgid "Public Current URL"
msgstr "Openbare actuele URL"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__public_url
msgid "Public Form for this form builder version"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__public_current_url
msgid "Public Form with versioning (same Name) in state Current"
msgstr "Openbaar formulier met versiebeheer (dezelfde naam) in status Actueel."

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_save_draft_done_url
#: model:ir.model.fields,field_description:formio.field_formio_form__public_save_draft_done_url
msgid "Public Save-Draft Done URL"
msgstr "Openbaar URL na concept-opslag"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_scroll_into_view_selector
msgid "Public Scroll Into View Selector"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_submit_done_url
#: model:ir.model.fields,field_description:formio.field_formio_form__public_submit_done_url
msgid "Public Submit Done URL"
msgstr "Openbaar URL na indiening"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__public_url
msgid "Public URL"
msgstr "Openbare URL"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__public_access_interval_number
msgid ""
"Public access to submitted Form shall be rejected after expiration of the "
"configured time interval."
msgstr ""
"Openbare toegang tot het ingediende formulier wordt geweigerd na het "
"verstrijken van het geconfigureerde tijdsinterval."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Publish"
msgstr "Publiceren"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Publish on Portal"
msgstr "Publiceer op portaal"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Publish to Public"
msgstr "Publiceer op website"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__res_id
msgid "Record ID"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__initial_res_id
msgid "Record ID #1"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__res_name
msgid "Record Name"
msgstr "Record naam"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Redirect After Save Draft"
msgstr "Doosturen na concept opslaan"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Redirect After Submit"
msgstr "Doorsturen na ingediend"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Redirect URL"
msgstr "Doorstuur URL"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_checker_wizard
msgid "Register Available Versions"
msgstr "Registreer beschikbare versies"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "Register Versions"
msgstr "Registreer beschikbare versies"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__res_act_window_url
msgid "Res Act Window Url"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__res_model
msgid "Res Model"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_form
msgid "Reset (download and reinstall)"
msgstr "Reset (downloaden en herinstalleer)"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Resource"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__formio_res_model_id
#: model:ir.model.fields,field_description:formio.field_formio_form__res_model_id
#: model_terms:ir.ui.view,arch_db:formio.view_formio_res_model_tree
msgid "Resource Model"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__initial_res_model_id
msgid "Resource Model #1"
msgstr "Resource model #1"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__res_model
msgid "Resource Model Name"
msgstr "Resource model naam"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__initial_res_model
msgid "Resource Model Name #1"
msgstr "Resource model naam #1"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_res_model
msgid "Resource Models"
msgstr "Resource modellen"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__res_model_name
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Resource Name"
msgstr "Resource Naam"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__initial_res_model_name
msgid "Resource Name #1"
msgstr "Resource naam #1"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__res_partner_id
msgid "Resource Partner"
msgstr "Resource partner"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_tree
msgid "Resource Type"
msgstr "Resource type"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_tree
msgid "Resource Type #1"
msgstr "Resource type #1"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__activity_user_id
#: model:ir.model.fields,field_description:formio.field_formio_form__activity_user_id
#: model:ir.model.fields,field_description:formio.field_formio_license__activity_user_id
#: model:ir.model.fields,field_description:formio.field_formio_version__activity_user_id
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__message_has_sms_error
#: model:ir.model.fields,field_description:formio.field_formio_form__message_has_sms_error
#: model:ir.model.fields,field_description:formio.field_formio_license__message_has_sms_error
#: model:ir.model.fields,field_description:formio.field_formio_version__message_has_sms_error
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__svg
msgid "SVG Font File"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Save Draft on Change Page"
msgstr "Concept opslaan na wijziging pagina"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Scroll Into View Selector"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"Select a default Form Builder JavaScript Options Template. It's contents, "
"the Form Builder JavaScript Options, shall be shown below."
msgstr ""
"Selecteer een standaardsjabloon voor de Form Builder JavaScript Opties. De "
"Form Builder JavaScript Opties worden hieronder weergegeven."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Select formio.js Options"
msgstr "Selecteer formio.js opties"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Send Invitation Mail"
msgstr "Verstuur Uitnodigingsmail"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__sequence
#: model:ir.model.fields,field_description:formio.field_formio_version__sequence
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__sequence
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__sequence
msgid "Sequence"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_ir_actions_server
msgid "Server Action"
msgstr "Serveractie"

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_builder.py:0
#: model:ir.model.fields,field_description:formio.field_formio_builder__server_action_ids
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#, python-format
msgid "Server Actions"
msgstr "Serveracties"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_config_settings
#: model:ir.ui.menu,name:formio.menu_formio_config_settings
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Settings"
msgstr "Instellingen"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__public_share
msgid "Share form in public? (with access expiration check)."
msgstr ""
"Maak het formulier openbaar? (met automatische toegang vervalcontrole)."

#. module: formio
#: model:ir.model.fields,field_description:formio.field_res_lang__formio_short_code
msgid "Short Code"
msgstr "Korte code"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__show_api_alert
msgid "Show Api Alert"
msgstr "API melding weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__show_form_id
msgid "Show Form ID"
msgstr "Formulier ID weergeven"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__show_form_id
msgid "Show Form ID in the Form header."
msgstr "Formulier ID in de Form header weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__show_form_state
msgid "Show Form State"
msgstr "Formulier status weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__show_form_title
msgid "Show Form Title"
msgstr "Formulier titel weergeven"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__show_form_title
msgid "Show Form Title in the Form header."
msgstr "Formulier titel in de Form header weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__show_form_uuid
msgid "Show Form UUID"
msgstr "Formulier UUID weergeven"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__show_form_uuid
msgid "Show Form UUID in the Form."
msgstr "Formulier UUID weergeven in formulier"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__show_id
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Show ID"
msgstr "ID weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__show_state
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Show State"
msgstr "Status weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__show_title
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Show Title"
msgstr "Titel weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__show_uuid
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Show UUID"
msgstr "UUID weergeven"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__show_form_user_metadata
#: model:ir.model.fields,field_description:formio.field_formio_form__show_user_metadata
msgid "Show User Metadata"
msgstr "Gebruiker metadata weergeven"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__show_form_user_metadata
msgid "Show submission and assigned user metadata in the Form header."
msgstr ""
"Toon informaitie over indiening en toegewezen gebruiker in de formulier "
"header."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__show_form_state
msgid "Show the state in the Form header."
msgstr "Status weergeven in de formulier header"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__source_property
msgid "Source Property"
msgstr "Broneigenschap"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_translation__source_id
#: model:ir.model.fields,field_description:formio.field_formio_translation_source__source
msgid "Source Term"
msgstr "Bronterm"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__source
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__source_text
msgid "Source Text"
msgstr "Brontekst"

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#: model:ir.model.fields,field_description:formio.field_formio_builder__state
#: model:ir.model.fields,field_description:formio.field_formio_form__state
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__state
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_search
#, python-format
msgid "State"
msgstr "Status"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_kanban
msgid "State:"
msgstr "Status:"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__activity_state
#: model:ir.model.fields,help:formio.field_formio_form__activity_state
#: model:ir.model.fields,help:formio.field_formio_license__activity_state
#: model:ir.model.fields,help:formio.field_formio_version__activity_state
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Submission"
msgstr "Indiening"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__submission_data
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Submission Data"
msgstr "Inzending data"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__submission_date
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Submission Date"
msgstr "Ingediend op"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__submission_partner_id
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Submission Partner"
msgstr "Ingediend door partner"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__submission_partner_name
msgid "Submission Partner Name"
msgstr "Ingediend door partner naam"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__submission_timezone
msgid "Submission Timezone"
msgstr "Indiening tijdzone"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__submission_user_id
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_form
msgid "Submission User"
msgstr "Ingediend door"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid "Submission date"
msgstr "Ingediend op"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid "Submit a Form"
msgstr "Formulier indienen"

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__ttf
msgid "TTF Font File"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__public_access
msgid ""
"The Public Access check. Computed public access by checking whether (field) "
"Public Access From has been expired."
msgstr ""
"De openbare toegangscontrole. Er wordt telkens berekend of de openbare "
"toegang van het formulier is verlopen."

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_builder.py:0
#, python-format
msgid "The field 'Public Access Rule' Type is required for Public Forms!"
msgstr ""
"Het veld 'Openbare toegang regeltype' is vereist voor openbare formulieren!"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__title
msgid "The form title in the current language"
msgstr "De formulier titel in de huidige taal"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
msgid "There are no Forms."
msgstr "Er zijn geen formulieren."

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_form.py:0
#, python-format
msgid "There is no Form Builder available to link this form to."
msgstr "Er is geen Form Builder beschikbaar om dit formulier aan te koppelen."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__invitation_mail_template_id
msgid ""
"This e-mail template will be sent on user assignment. Leave empty to send "
"nothing."
msgstr ""
"Dit e-mailsjabloon wordt verzonden in opdracht van de gebruiker. Laat leeg "
"om niet te verzenden."

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__public_access_rule_type__time_interval
msgid "Time Interval"
msgstr "Tijdsinterval"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__title
#: model:ir.model.fields,field_description:formio.field_formio_form__title
#: model_terms:ir.ui.view,arch_db:formio.portal_my_formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_search
msgid "Title"
msgstr "Titel"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder_translation__value
msgid "Translated Value"
msgstr "Vertalingswaarde"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_translation_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_translation_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_translation_form
msgid "Translation"
msgstr "Vertaling"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_translation_source_form
msgid "Translation Source"
msgstr "Vertaling Bronterm"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_translation_source
#: model:ir.ui.menu,name:formio.menu_formio_translation_source
#: model_terms:ir.ui.view,arch_db:formio.view_formio_translation_source_tree
msgid "Translation Sources"
msgstr "Vertalingen Brontermen"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_translation__value
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__value
msgid "Translation Value"
msgstr "Vertalingswaarde"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_builder_translation
#: model:ir.actions.act_window,name:formio.action_formio_translation
#: model:ir.actions.act_window,name:formio.action_formio_version_translation
#: model:ir.model.fields,field_description:formio.field_formio_builder__translations
#: model:ir.model.fields,field_description:formio.field_formio_version__translation_ids
#: model:ir.ui.menu,name:formio.menu_formio_translation
#: model:ir.ui.menu,name:formio.menu_formio_translation_root
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_translation_tree
#: model_terms:ir.ui.view,arch_db:formio.view_formio_translation_tree
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_translation_tree
msgid "Translations"
msgstr "Vertalingen"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__type
msgid "Type"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__activity_exception_decoration
#: model:ir.model.fields,help:formio.field_formio_form__activity_exception_decoration
#: model:ir.model.fields,help:formio.field_formio_license__activity_exception_decoration
#: model:ir.model.fields,help:formio.field_formio_version__activity_exception_decoration
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_default_asset_css__url
msgid "URL"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__uuid
#: model:ir.model.fields,field_description:formio.field_formio_form__uuid
msgid "UUID"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_form
msgid "Unlink Base Translations"
msgstr "Ontkoppel basis vertalingen"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Unlock"
msgstr "Ontgrendelen"

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#, python-format
msgid "Unlocked"
msgstr "Ontgrendeld"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__base_translation_updated
msgid "Updated Base"
msgstr "Bijgewerkte basis"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_translation_search
msgid "Updated Base Translations"
msgstr "Update basis vertalingen"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_form__url
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__url
msgid "Url"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "Use ASCII letters, digits (0-9), - or _"
msgstr "Gebruik ASCII letters, cijfers, - of _"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__backend_use_draft
msgid "Use Draft in Backend"
msgstr "Gebruik \"concept\" in backend"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__backend_use_obsolete
msgid "Use Obsolete in Backend"
msgstr "Gebruik \"verouderd\" in backend"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__sequence
msgid "Usefull when storing and listing forms in an ordered way"
msgstr ""
"Handig om formulieren op een geordende manier op te slaan en weer te geven"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__allow_force_update_state_group_ids
msgid ""
"User groups allowed to manually force an update of the Form state.If no "
"groups are specified it's allowed for every user."
msgstr ""
"De gebruikersgroepen welke handmatig een update van de formulierstatus mogen "
"doen.\n"
"Als er geen groepen zijn gespecificeerd, dan het is toegestaan voor elke "
"gebruiker."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_form__submission_user_id
msgid "User who submitted the form."
msgstr "Gebruiker die het formulier had ingediend."

#. module: formio
#: model:res.groups,name:formio.group_formio_user_all_forms
msgid "User: All forms"
msgstr "Gebruiker: Alle formulieren"

#. module: formio
#: model:res.groups,name:formio.group_formio_user
msgid "User: Assigned forms"
msgstr "Gebruiker: Toegewezen formulieren"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_license__valid_until_date
msgid "Valid Until"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder_js_options__value
msgid "Value (JSON)"
msgstr "Waarde (JSON)"

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#: model:ir.model.fields,field_description:formio.field_formio_builder__version
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_tree
#, python-format
msgid "Version"
msgstr "Versie"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag_available__version_checker_wizard_id
msgid "Version Checker Wizard"
msgstr "Versie controle wizard"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__version_comment
msgid "Version Comment"
msgstr "Versie Commentaar"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_tag_search
msgid "Version GitHub tags"
msgstr "Versie GitHub tags"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__version_name
msgid "Version Name"
msgstr "Versienaam"

#. module: formio
#: model:ir.ui.menu,name:formio.menu_formio_version_root
msgid "Versions"
msgstr "Versies"

#. module: formio
#. odoo-python
#: code:addons/formio/wizard/formio_version_github_checker_wizard.py:0
#, python-format
msgid "Versions GitHub tags"
msgstr "Versies GitHub tags"

#. module: formio
#: model:ir.model,name:formio.model_ir_ui_view
msgid "View"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:formio.field_ir_ui_view__type
msgid "View Type"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__view_as_html
msgid "View as HTML"
msgstr "Weegeven als HTML"

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__view_as_html
msgid "View submission as a HTML view instead of disabled webform."
msgstr "Indiening weergeven als HTML-view i.p.v. een disabled webform."

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__woff
msgid "WOFF Font File"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_version_asset__type__woff2
msgid "WOFF2 Font File"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__website_message_ids
#: model:ir.model.fields,field_description:formio.field_formio_form__website_message_ids
#: model:ir.model.fields,field_description:formio.field_formio_license__website_message_ids
#: model:ir.model.fields,field_description:formio.field_formio_version__website_message_ids
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__website_message_ids
#: model:ir.model.fields,help:formio.field_formio_form__website_message_ids
#: model:ir.model.fields,help:formio.field_formio_license__website_message_ids
#: model:ir.model.fields,help:formio.field_formio_version__website_message_ids
#: model:ir.model.fields,help:formio.field_formio_version_github_tag__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"When adding a new Form Builder, use this formio.js client/library version."
msgstr ""
"Gebruik bij het toevoegen van een nieuwe Form Builder deze formio.js client/"
"library versie."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__form_copy_to_current
#: model:ir.model.fields,help:formio.field_formio_form__copy_to_current
msgid ""
"When copying a form, always link it to the current version of the builder "
"instead of the original builder."
msgstr ""
"Bij het kopiëren van een formulier, altijd koppelen aan de huidige versie "
"van de builder in plaats van de oorspronkelijke builder."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__debug
msgid ""
"When enabled along the standard Developer Mode (debug mode), this provides "
"server log output etc."
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__backend_submission_url_add_query_params_from__window
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__portal_submission_url_add_query_params_from__window
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__public_submission_url_add_query_params_from__window
msgid "Window iframe (src)"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__backend_submission_url_add_query_params_from__window_parent
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__portal_submission_url_add_query_params_from__window_parent
#: model:ir.model.fields.selection,name:formio.selection__formio_builder__public_submission_url_add_query_params_from__window_parent
msgid "Window parent (URL)"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__wizard
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_search
msgid "Wizard"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__wizard_on_change_page_save_draft
msgid "Wizard on Change Page Save Draft"
msgstr "Wizard bij Pagina wijzigen Concept opslaan"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__wizard_on_next_page_save_draft
msgid "Wizard on Next Page Save Draft"
msgstr "Wizard bij volgende pagina, Concept opslaan"

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_builder.py:0
#, python-format
msgid "Write comment about version %s ..."
msgstr "Schrijf commentaar over versie %s ..."

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_default_asset_css__attachment_type
#: model:ir.model.fields,help:formio.field_formio_version_asset__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"U kunt een bestand vanaf uw apparaat uploaden of kopieer/plak een link naar "
"het bestand."

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_form.py:0
#, python-format
msgid "You're not allowed to (force) update the Form into Cancel state."
msgstr ""
"U mag het formulier niet (geforceerd) bijwerken naar de status Annuleren."

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_form.py:0
#, python-format
msgid "You're not allowed to (force) update the Form into Complete state."
msgstr ""
"U mag het formulier niet (geforceerd) bijwerken naar de status Voltooid."

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_form.py:0
#, python-format
msgid "You're not allowed to (force) update the Form into Draft state."
msgstr ""
"U mag het formulier niet (geforceerd) bijwerken naar de status Concept."

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_form.py:0
#, python-format
msgid "You're not allowed to copy this form."
msgstr "U mag het formulier niet kopieren."

#. module: formio
#: model:mail.template,subject:formio.mail_invitation_internal_user
#: model:mail.template,subject:formio.mail_invitation_portal_user
msgid "[Form] {{ object.title }}"
msgstr "[Formulier] {{ object.title }}"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_version_github_checker_wizard
msgid "formio (js) Version GitHub Importer"
msgstr "formio (js) Versie GitHub Importer"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_formio
msgid "formio Builder"
msgstr "formio Builder"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_form_formio
msgid "formio Form"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__ir_actions_act_window_view__view_mode__formio_builder
#: model:ir.model.fields.selection,name:formio.selection__ir_ui_view__type__formio_builder
msgid "formio builder"
msgstr ""

#. module: formio
#: model:ir.model.fields.selection,name:formio.selection__ir_actions_act_window_view__view_mode__formio_form
#: model:ir.model.fields.selection,name:formio.selection__ir_ui_view__type__formio_form
msgid "formio form"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_builder_js_options
msgid "formio.builder JavaScript Options"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_builder_translation
msgid "formio.builder Translation"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_ir_attachment__formio_form_id
msgid "formio.form"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "formio.js"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_translation
msgid "formio.js Base Translation"
msgstr "formio.js basis vertaling"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__formio_css_assets
msgid "formio.js CSS"
msgstr "formio.js CSS"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_res_config_settings__formio_default_asset_css_ids
msgid "formio.js CSS assets"
msgstr "formio.js CSS assets"

#. module: formio
#: model:ir.model,name:formio.model_formio_default_asset_css
msgid "formio.js Default Asset CSS"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__formio_js_assets
msgid "formio.js Javascript"
msgstr "formio.js JavaScript"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__formio_js_options
msgid "formio.js Javascript Options"
msgstr "formio.js JavaScript opties"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__formio_js_options_id
msgid "formio.js Javascript Options template"
msgstr "formio.js Javascript Opties template"

#. module: formio
#: model:ir.model,name:formio.model_formio_version
msgid "formio.js Version"
msgstr "formio.js versie"

#. module: formio
#: model:ir.model,name:formio.model_formio_version_asset
msgid "formio.js Version Asset"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_version_github_checker_wizard
msgid "formio.js Version GitHub Checker Wizard"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_version_github_tag
msgid "formio.js Version GitHub Tag"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_version_github_tag_available
msgid "formio.js Version GitHub Tag Available"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_version_translation
msgid "formio.js Version Translation"
msgstr ""

#. module: formio
#: model:ir.model,name:formio.model_formio_translation_source
msgid "formio.js Version Translation Source"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__base_translation_id
msgid "formio.js base translation"
msgstr "formio.js basis vertaling"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_res_config_settings__formio_default_builder_js_options
msgid "formio.js builder options"
msgstr "formio.js builder opties"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_res_config_settings__formio_default_builder_js_options_id
msgid "formio.js builder options ID"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "formio.js library (js, css) version importer"
msgstr "formio.js library (js, css) versie importer\""

#. module: formio
#: model:ir.model.fields,help:formio.field_formio_builder__formio_version_name
#: model:ir.model.fields,help:formio.field_formio_version__name
msgid "formio.js release/version."
msgstr "formio.js release/versie"

#. module: formio
#. odoo-javascript
#: code:addons/formio/static/src/js/views/formio_builder.xml:0
#: model:ir.model.fields,field_description:formio.field_formio_builder__formio_version_id
#: model:ir.model.fields,field_description:formio.field_formio_version_asset__version_id
#: model:ir.model.fields,field_description:formio.field_formio_version_github_tag__formio_version_id
#: model:ir.model.fields,field_description:formio.field_formio_version_translation__formio_version_id
#: model:ir.model.fields,field_description:formio.field_ir_attachment__formio_asset_formio_version_id
#: model:ir.model.fields,field_description:formio.field_res_config_settings__formio_default_version_id
#, python-format
msgid "formio.js version"
msgstr "formio.js versie"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__formio_version_name
msgid "formio.js version name"
msgstr "formio.js versie naam"

#. module: formio
#: model:ir.actions.act_window,name:formio.action_formio_version_github_tag
#: model:ir.ui.menu,name:formio.menu_formio_version_github_tag
msgid "formio.js versions (GitHub tags)"
msgstr "formio.js versies (GitHub tags)"

#. module: formio
#: model:ir.model.fields,field_description:formio.field_res_config_settings__formio_versions_to_register
msgid "formio.js versions to register"
msgstr "formio.js versies te registreren"

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"https://github.com/formio/formio.js/blob/master/src/components/file/editForm/"
"File.edit.file.js"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "https://github.com/formio/formio.js/tags"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid "iFrame Embedding"
msgstr ""

#. module: formio
#: model:ir.model.fields,field_description:formio.field_formio_builder__iframe_resizer_body_margin
#: model:ir.model.fields,field_description:formio.field_formio_form__iframe_resizer_body_margin
msgid "iFrame Resizer bodyMargin"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid ""
"is not required, but it can get a higher rate limit.\n"
"                                        This prevents timeouts and "
"authorization errors."
msgstr ""
"is niet vereist, maar kan een hogere snelheidslimiet krijgen.\n"
"Dit voorkomt time-outs en autorisatiefouten."

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "v4,v5"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
msgid "v4.17,v4.18"
msgstr ""

#. module: formio
#: model_terms:ir.ui.view,arch_db:formio.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:formio.view_formio_builder_form
msgid ""
"{\n"
"  'editForm': {\n"
"    'file': [\n"
"      {\n"
"        'key': 'file',\n"
"        'components': [\n"
"          {'key': 'webcam', 'defaultValue': True},\n"
"          {'key': 'storage', 'defaultValue': 'base64'}\n"
"        ]\n"
"      }\n"
"    ]\n"
"  }\n"
"}"
msgstr ""

#. module: formio
#. odoo-python
#: code:addons/formio/models/formio_builder.py:0
#, python-format
msgid "{title} (state: {state}, version: {version})"
msgstr "{title} [status: {state}, versie: {version}]"

#~ msgid "Public UUID"
#~ msgstr "Openbare UUID"
