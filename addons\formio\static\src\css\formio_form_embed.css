#formio_form_app {
    margin: 0 auto;
    padding-left: 4px;
    padding-right: 4px;
    clear: both;
}

#formio_form_loading_overlay {
    position: fixed;
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/web/static/img/spin.svg') center center no-repeat;
    /* black background with opacity */
    background-color: rgba(0,0,0,0.5);
    z-index: 9999;
    cursor: pointer;
}

.formio_form_dock {
    margin-bottom: .5rem;
}

.formio_form_dock * {
    font-size: 14px;
}

.formio_form_dock ul {
    list-style: none;
}

.formio_form_dock ul li {
    float: left;
    margin-left: 40px;
}

span.formio_dock_label {
    padding-right: 10px;
    font-weight: 700;
}

h3.formio_form_title {
    color: #000;
    font-size: 1.5em;
}

.formio_languages {
    clear: both;
    margin-bottom: 20px;
    padding-top: 4px;
    padding-left: 4px;
}

.formio_languages button {
    border: 1px solid #ced4da;
    background-color: #fff;
    color: #000;
    padding: 6px 10px 6px 10px;
    box-shadow: none !important;
    transition: none !important;
}

.btn.language_button_active {
    background-color: rgba(0, 0, 0, 0.08);
    color: #000;
}

.formio_languages button:hover {
    background-color: #d3d3d3;
    border: 1px solid #c0c0c0;
}

#formio_form_server_error {
    float: left;
    width: 100%;
}

/**
  WORKAROUND
  ----------
  - Browser: Safari 16.6
  - This fixes a Data Grid with Columns inside. The columns become too
    narrow in Safari!
  - GitHub issue: https://github.com/formio/formio.js/issues/5426
**/

#formio_form_app table.datagrid-table tbody tr td {
    width: auto;
}

a[data-tooltip]:hover {
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    text-decoration: none;
}

a[data-tooltip]:hover::before {
    position: absolute;
    content: attr(data-tooltip);
    padding: 0.2em 0.6em;
    white-space: nowrap;
    background-color: #000;
    color: #fff;
    border-radius: 5px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    font-size: 0.8em;
    font-weight: bold;
    transform: translate(-25%, calc(-100% - 10px));
    z-index: 999;
}
