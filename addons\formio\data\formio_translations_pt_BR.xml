<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright Nova Code (http://www.novacode.nl)
See LICENSE file for full licensing details. -->

<odoo>
    <!-- pt_BR -->
    <data noupdate="1">
        <record id="i18n_pt_BR_complete" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_complete"/>
            <field name="value">O formulário foi enviado</field>
        </record>
        <record id="i18n_pt_BR_error" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_error"/>
            <field name="value">Antes de salvar o formulário, você deve resolver os erros a seguir.</field>
        </record>
        <record id="i18n_pt_BR_alertMessage" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_alertMessage"/>
            <field name="value">{{message}}</field>
        </record>
        <record id="i18n_pt_BR_required" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_required"/>
            <field name="value">{{field}} é mandatório</field>
        </record>
        <record id="i18n_pt_BR_pattern" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_pattern"/>
            <field name="value">{{field}} não está de acordo com o formato {{pattern}}</field>
        </record>
        <record id="i18n_pt_BR_minLength" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_minLength"/>
            <field name="value">{{field}} deve ser mais longo que {{length}} caracteres.</field>
        </record>
        <record id="i18n_pt_BR_maxLength" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_maxLength"/>
            <field name="value">{{field}} deve ser mais curto que {{length}} caracteres.</field>
        </record>
        <record id="i18n_pt_BR_min" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_min"/>
            <field name="value">{{field}} não pode ser menor que {{min}}.</field>
        </record>
        <record id="i18n_pt_BR_max" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_max"/>
            <field name="value">{{field}} não pode ser maior que {{max}}.</field>
        </record>
        <record id="i18n_pt_BR_maxDate" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_maxDate"/>
            <field name="value">{{field}} não deve conter uma data posterior {{- maxDate}}.</field>
        </record>
        <record id="i18n_pt_BR_minDate" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_minDate"/>
            <field name="value">{{field}} não deve conter uma data para {{- minDate}}.</field>
        </record>
        <record id="i18n_pt_BR_invalid_email" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_invalid_email"/>
            <field name="value">{{field}} Deve ser um endereço de e-mail válido.</field>
        </record>
        <record id="i18n_pt_BR_invalid_url" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_invalid_url"/>
            <field name="value">{{field}} deve ser um URL válido.</field>
        </record>
        <record id="i18n_pt_BR_invalid_regex" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_invalid_regex"/>
            <field name="value">{{field}} não corresponde ao padrão.</field>
        </record>
        <record id="i18n_pt_BR_invalid_date" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_invalid_date"/>
            <field name="value">{{field}} não é uma data válida.</field>
        </record>
        <record id="i18n_pt_BR_invalid_day" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_invalid_day"/>
            <field name="value">{{field}} não é um dia válido.</field>
        </record>
        <record id="i18n_pt_BR_mask" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_invalid_mask"/>
            <field name="value">{{field}} não corresponde à máscara de entrada.</field>
        </record>
        <record id="i18n_pt_BR_stripe" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_stripe"/>
            <field name="value">{{stripe}}</field>
        </record>
        <record id="i18n_pt_BR_month" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_month"/>
            <field name="value">Mês</field>
        </record>
        <record id="i18n_pt_BR_day" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_day"/>
            <field name="value">Dia</field>
        </record>
        <record id="i18n_pt_BR_year" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_year"/>
            <field name="value">Ano</field>
        </record>
        <record id="i18n_pt_BR_january" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_january"/>
            <field name="value">janeiro</field>
        </record>
        <record id="i18n_pt_BR_february" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_february"/>
            <field name="value">fevereiro</field>
        </record>
        <record id="i18n_pt_BR_march" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_march"/>
            <field name="value">março</field>
        </record>
        <record id="i18n_pt_BR_april" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_april"/>
            <field name="value">abril</field>
        </record>
        <record id="i18n_pt_BR_may" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_may"/>
            <field name="value">maio</field>
        </record>
        <record id="i18n_pt_BR_june" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_june"/>
            <field name="value">junho</field>
        </record>
        <record id="i18n_pt_BR_july" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_july"/>
            <field name="value">julho</field>
        </record>
        <record id="i18n_pt_BR_august" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_august"/>
            <field name="value">agosto</field>
        </record>
        <record id="i18n_pt_BR_september" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_september"/>
            <field name="value">setembro</field>
        </record>
        <record id="i18n_pt_BR_october" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_october"/>
            <field name="value">outubro</field>
        </record>
        <record id="i18n_pt_BR_november" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_november"/>
            <field name="value">novembro</field>
        </record>
        <record id="i18n_pt_BR_december" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_december"/>
            <field name="value">dezembro</field>
        </record>
        <record id="i18n_pt_BR_next" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_next"/>
            <field name="value">Próximo</field>
        </record>
        <record id="i18n_pt_BR_previous" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_previous"/>
            <field name="value">Anterior</field>
        </record>
        <record id="i18n_pt_BR_cancel" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_cancel"/>
            <field name="value">Cancelar</field>
        </record>
        <record id="i18n_pt_BR_submit" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_submit"/>
            <field name="value">Enviar formulário</field>
        </record>
        <record id="i18n_pt_BR_submitError" model="formio.translation">
            <field name="lang_id" ref="base.lang_pt_BR"/>
            <field name="source_id" ref="formio.i18n_submitError"/>
            <field name="value">Verifique o formulário e corrija todos os erros antes de enviar.</field>
        </record>
    </data>
</odoo>
