body {
    /* Remove horizontal scrollbar, due to formio.js 'margin: 15px' on div below:

       <div id="formio_builder_app">
           <div id="formio_builder">
               <div class="formio builder row formbuilder"
   */
    margin-right: 15px;
}

#formio_builder_app {
    margin: 0 auto;
    padding-left: 4px;
    padding-right: 4px;
}

#formio_builder_loading {
    background: url('/web/static/lib/select2/select2-spinner.gif') center center no-repeat;
    height: 16px;
    margin-top: 5%;
}

/* languages buttons */

.formio_languages {
    clear: both;
    margin-bottom: 1rem;
    padding-left: 4px;
}

.formio_languages button {
    border: 1px solid #ced4da;
    background-color: #fff;
    color: #000;
    padding: 6px 10px 6px 10px;
    box-shadow: none !important;
    transition: none !important;
}

.btn.language_button_active {
    background-color: rgba(0, 0, 0, 0.08);
    color: #000;
}

.formio_languages button:hover {
    background-color: #d3d3d3;
    border: 1px solid #c0c0c0;
}

.formio_builder_actions {
    text-align: left;
    padding: 0px 10px;
}

/* actions buttons */

#formio_builder_actions_bottom {
    margin-top: 1rem;
}

.formio_builder_actions a {
    margin: 0 0 1rem 0;
}

.formio_builder_actions a i {
    padding-right: 0.2rem;
}

.formio_builder_actions div.formio_text {
    margin: 0 0 1rem 0;
}

.formio_no_autosave_border {
    box-shadow: inset 0 0 10px #ffc107;
    padding-top: 8px;
}

/**
  Hack:
  Prevent the comopnent-editor-dialog scale too much
**/
/* .component-edit-tabs.col-sm-6 { */
/*     min-height: auto; */
/* } */
